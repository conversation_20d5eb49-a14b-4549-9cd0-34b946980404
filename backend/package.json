{"name": "gemini-cli-nodejs", "version": "1.0.0", "description": "Node.js version of Gemini CLI API server", "type": "module", "main": "dist/code-assist-api.js", "scripts": {"build": "tsc", "start": "node dist/code-assist-api.js", "dev": "ts-node --esm src/code-assist-api.ts", "start:env": "./start.sh"}, "dependencies": {"@google/gemini-cli-core": "^0.1.4", "gaxios": "^6.7.1", "express": "^4.18.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "ts-node": "^10.9.1", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}