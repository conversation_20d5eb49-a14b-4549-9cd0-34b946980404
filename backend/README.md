# 🔧 Gemini CLI Backend

后端 API 服务，提供 Gemini 模型的接口代理。

## 📁 目录结构

```
backend/
├── src/              # TypeScript源代码
│   └── code-assist-api.ts
├── dist/             # 编译后的JavaScript
├── scripts/          # 启动和测试脚本
│   ├── start-dev.sh     # 开发环境启动
│   ├── start.sh         # 生产环境启动
│   ├── test-api.sh      # API测试
│   ├── test-complete.sh # 完整测试
│   ├── test-detailed.sh # 详细测试
│   └── test-streaming.sh # 流式测试
├── package.json      # 项目配置
└── tsconfig.json     # TypeScript配置
```

## 🚀 启动方式

### 首次使用 (推荐)

```bash
# 1. 设置默认OAuth认证
gemini auth login

# 2. 启动开发服务器
./scripts/start-dev.sh
```

### 日常使用

```bash
# 开发环境 (使用默认账户)
./scripts/start-dev.sh

# 生产环境
./scripts/start.sh
```

### 多账户使用

```bash
# 临时切换账户
GEMINI_OAUTH_CREDS=/path/to/account.json ./scripts/start-dev.sh

# 使用管理工具
./scripts/manage-accounts.sh
```

## 🧪 测试脚本

```bash
# API基础测试
./scripts/test-api.sh

# 完整功能测试
./scripts/test-complete.sh

# 详细测试报告
./scripts/test-detailed.sh

# 流式传输测试
./scripts/test-streaming.sh
```

## 🔧 环境要求

-   Node.js >= 18.0.0
-   TypeScript
-   认证配置 (见下方)
-   代理配置 (如需要)

## 🔐 认证配置

仅支持 Google 个人账户 OAuth 认证，支持多账户切换：

#### 默认账户

```bash
gemini auth login  # 保存到 ~/.gemini/oauth_creds.json
```

### 多账户支持

```bash
# 方式1: 使用 GOOGLE_APPLICATION_CREDENTIALS
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/account1_oauth_creds.json

# 方式2: 使用 GEMINI_OAUTH_CREDS (推荐)
export GEMINI_OAUTH_CREDS=/path/to/account1_oauth_creds.json

# 使用多账户管理工具
./scripts/manage-accounts.sh
```

## 📡 API 端点

-   **Token 计数**: `POST /api/{model}:countTokens`
-   **内容生成**: `POST /api/{model}:generateContent`
-   **流式生成**: `POST /api/{model}:streamGenerateContent`
-   **内容嵌入**: `POST /api/{model}:embedContent`

## 🎯 支持的模型

-   Gemini 2.5: flash, pro, flash-lite
-   Gemini 2.0: flash, flash-live
-   Gemini 1.5: flash, pro
-   LearnLM: 教育专用模型
