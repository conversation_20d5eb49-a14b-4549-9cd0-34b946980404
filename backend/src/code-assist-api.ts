import { AuthType, createCodeAssistContentGenerator } from "@google/gemini-cli-core";
import { arch, env, platform } from "node:process";
import { GaxiosError } from "gaxios";
import express from "express";
import { promises as fs } from "node:fs";
import path from "node:path";
import os from "node:os";

env.CLI_VERSION ||= "0.1.4";

const app = express();
const port = process.env.PORT || 8000;

// Middleware to parse JSON with increased size limit for large code contexts
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ limit: '50mb', extended: true }));

// CORS middleware for web frontend
app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");

    if (req.method === "OPTIONS") {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Initialize code assist
let codeAssist: any;

async function initializeCodeAssist() {
    try {
        // 使用Google个人账户认证 (唯一支持的认证方式)
        const authType = AuthType.LOGIN_WITH_GOOGLE_PERSONAL;
        let authInfo = "";

        // 检查是否指定了自定义OAuth凭证文件
        if (env.GOOGLE_APPLICATION_CREDENTIALS) {
            authInfo = `自定义凭证: ${env.GOOGLE_APPLICATION_CREDENTIALS}`;
            console.log(`👤 使用 Google 个人账户认证 (${authInfo})`);
        } else if (env.GEMINI_OAUTH_CREDS) {
            // 支持自定义环境变量指定凭证文件
            env.GOOGLE_APPLICATION_CREDENTIALS = env.GEMINI_OAUTH_CREDS;
            authInfo = `自定义凭证: ${env.GEMINI_OAUTH_CREDS}`;
            console.log(`👤 使用 Google 个人账户认证 (${authInfo})`);
        } else {
            authInfo = "默认凭证: ~/.gemini/oauth_creds.json";
            console.log(`👤 使用 Google 个人账户认证 (${authInfo})`);
        }

        codeAssist = await createCodeAssistContentGenerator(
            {
                headers: {
                    "User-Agent": `GeminiCLI/${env.CLI_VERSION} (${platform}; ${arch})`
                }
            },
            authType
        );

        console.log(`✅ Code assist 初始化成功 (${authInfo})`);
    } catch (error) {
        console.error("❌ Code assist 初始化失败:", error);
        console.error("\n🔧 请检查OAuth认证配置:");
        console.error("1. 运行 'gemini auth login' 设置默认账户 (推荐)");
        console.error("2. 设置 'export GEMINI_OAUTH_CREDS=/path/to/oauth_creds.json' (多账户)");
        console.error("3. 使用多账户管理工具: ./scripts/manage-accounts.sh");
        console.error("\n💡 如果是首次使用，服务器会自动打开浏览器进行OAuth认证");
        process.exit(1);
    }
}

// 账户配置管理API
const ACCOUNTS_DIR = path.join(os.homedir(), ".gemini-accounts");
const PROJECTS_DIR = path.join(os.homedir(), ".gemini-projects");
const DEFAULT_CREDS = path.join(os.homedir(), ".gemini", "oauth_creds.json");

// 获取当前登录信息
app.get("/api/config/current", async (req, res) => {
    try {
        const hasDefaultCreds = await fs
            .access(DEFAULT_CREDS)
            .then(() => true)
            .catch(() => false);

        if (hasDefaultCreds) {
            res.json({
                success: true,
                isLoggedIn: true,
                credentialsPath: DEFAULT_CREDS,
                projectId: env.GOOGLE_CLOUD_PROJECT || env.GEMINI_PROJECT_ID || "未设置"
            });
        } else {
            res.json({
                success: true,
                isLoggedIn: false,
                message: "未找到登录凭证"
            });
        }
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "检查登录状态失败: " + error.message
        });
    }
});

// 保存当前登录信息
app.post("/api/config/save", async (req, res) => {
    try {
        const { accountName } = req.body;

        if (!accountName) {
            return res.status(400).json({
                success: false,
                error: "账户名称不能为空"
            });
        }

        // 确保目录存在
        await fs.mkdir(ACCOUNTS_DIR, { recursive: true });
        await fs.mkdir(PROJECTS_DIR, { recursive: true });

        // 复制当前凭证文件
        const targetCreds = path.join(ACCOUNTS_DIR, `${accountName}.json`);
        const targetProject = path.join(PROJECTS_DIR, `${accountName}.txt`);

        await fs.copyFile(DEFAULT_CREDS, targetCreds);
        await fs.writeFile(targetProject, env.GOOGLE_CLOUD_PROJECT || env.GEMINI_PROJECT_ID || "");

        res.json({
            success: true,
            message: `账户 "${accountName}" 已保存`,
            accountName
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "保存账户失败: " + error.message
        });
    }
});

// 清除当前登录信息
app.post("/api/config/clear", async (req, res) => {
    try {
        // 删除OAuth凭证文件
        await fs.unlink(DEFAULT_CREDS).catch(() => {});

        // 清除环境变量 (注意：这只影响当前进程)
        delete env.GOOGLE_CLOUD_PROJECT;
        delete env.GEMINI_PROJECT_ID;

        res.json({
            success: true,
            message: "登录信息已清除，请重启服务器以生效",
            needRestart: true
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "清除登录信息失败: " + error.message
        });
    }
});

// 获取已保存的账户列表
app.get("/api/config/accounts", async (req, res) => {
    try {
        const accounts = [];

        // 检查目录是否存在
        const hasAccountsDir = await fs
            .access(ACCOUNTS_DIR)
            .then(() => true)
            .catch(() => false);

        if (hasAccountsDir) {
            const files = await fs.readdir(ACCOUNTS_DIR);
            for (const file of files) {
                if (file.endsWith(".json")) {
                    const accountName = file.replace(".json", "");
                    const projectFile = path.join(PROJECTS_DIR, `${accountName}.txt`);
                    let projectId = "未知";

                    try {
                        projectId = await fs.readFile(projectFile, "utf-8");
                    } catch {
                        // 项目ID文件不存在
                    }

                    accounts.push({
                        name: accountName,
                        projectId: projectId.trim()
                    });
                }
            }
        }

        res.json({
            success: true,
            accounts
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "获取账户列表失败: " + error.message
        });
    }
});

// 切换账户
app.post("/api/config/switch", async (req, res) => {
    try {
        const { accountName } = req.body;

        if (!accountName) {
            return res.status(400).json({
                success: false,
                error: "账户名称不能为空"
            });
        }

        if (accountName === "default") {
            // 切换到默认账户
            delete env.GOOGLE_APPLICATION_CREDENTIALS;
            delete env.GEMINI_OAUTH_CREDS;
            delete env.GEMINI_PROJECT_ID;

            res.json({
                success: true,
                message: "已切换到默认账户，请重启服务器以生效",
                needRestart: true
            });
        } else {
            // 切换到指定账户
            const targetCreds = path.join(ACCOUNTS_DIR, `${accountName}.json`);
            const targetProject = path.join(PROJECTS_DIR, `${accountName}.txt`);

            // 检查文件是否存在
            const hasCredsFile = await fs
                .access(targetCreds)
                .then(() => true)
                .catch(() => false);
            const hasProjectFile = await fs
                .access(targetProject)
                .then(() => true)
                .catch(() => false);

            if (!hasCredsFile || !hasProjectFile) {
                return res.status(404).json({
                    success: false,
                    error: "账户配置文件不存在"
                });
            }

            const projectId = await fs.readFile(targetProject, "utf-8");

            // 设置环境变量
            env.GEMINI_OAUTH_CREDS = targetCreds;
            env.GEMINI_PROJECT_ID = projectId.trim();

            res.json({
                success: true,
                message: `已切换到账户 "${accountName}"，请重启服务器以生效`,
                accountName,
                projectId: projectId.trim(),
                needRestart: true
            });
        }
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "切换账户失败: " + error.message
        });
    }
});

// 删除账户
app.delete("/api/config/accounts/:accountName", async (req, res) => {
    try {
        const { accountName } = req.params;

        if (!accountName || accountName === "default") {
            return res.status(400).json({
                success: false,
                error: "无效的账户名称"
            });
        }

        const targetCreds = path.join(ACCOUNTS_DIR, `${accountName}.json`);
        const targetProject = path.join(PROJECTS_DIR, `${accountName}.txt`);

        // 删除文件
        await fs.unlink(targetCreds).catch(() => {});
        await fs.unlink(targetProject).catch(() => {});

        res.json({
            success: true,
            message: `账户 "${accountName}" 已删除`
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "删除账户失败: " + error.message
        });
    }
});

// OpenAI Compatible API endpoint for Roo Code integration
app.post("/v1/chat/completions", async (req, res) => {
    try {
        const { messages, model = "gemini-2.5-flash", stream = false, max_tokens, temperature } = req.body;

        if (!messages || !Array.isArray(messages)) {
            return res.status(400).json({
                error: {
                    message: "Invalid request: messages array is required",
                    type: "invalid_request_error"
                }
            });
        }

        // 转换 OpenAI 格式到 Gemini 格式
        const contents = messages
            .filter((msg) => msg.role !== "system") // Gemini API 不支持 system 角色
            .map((msg) => {
                // 确保 content 是字符串
                const content = typeof msg.content === "string" ? msg.content : JSON.stringify(msg.content);

                return {
                    role: msg.role === "assistant" ? "model" : msg.role,
                    parts: [{ text: content }]
                };
            });

        // 如果有 system 消息，将其作为 systemInstruction 处理
        const systemMessage = messages.find((msg) => msg.role === "system");
        const systemInstruction = systemMessage ? { text: typeof systemMessage.content === "string" ? systemMessage.content : JSON.stringify(systemMessage.content) } : undefined;

        const generationConfig = {
            maxOutputTokens: max_tokens || 8192,
            temperature: temperature || 0.7
        };

        if (stream) {
            // 流式响应
            res.setHeader("Content-Type", "text/event-stream");
            res.setHeader("Cache-Control", "no-cache");
            res.setHeader("Connection", "keep-alive");
            res.setHeader("Access-Control-Allow-Origin", "*");

            const streamResponse = await codeAssist.generateContentStream({
                model,
                contents,
                config: {
                    ...generationConfig,
                    systemInstruction
                }
            });

            let fullContent = "";
            for await (const chunk of streamResponse) {
                // chunk 结构: { candidates: [{ content: { parts: [{ text: "..." }] } }] }
                const text = chunk.candidates?.[0]?.content?.parts?.[0]?.text || "";
                fullContent += text;

                const sseData = {
                    id: `chatcmpl-${Date.now()}`,
                    object: "chat.completion.chunk",
                    created: Math.floor(Date.now() / 1000),
                    model,
                    choices: [
                        {
                            index: 0,
                            delta: { content: text },
                            finish_reason: null
                        }
                    ]
                };

                res.write(`data: ${JSON.stringify(sseData)}\n\n`);
            }

            // 发送结束标记
            const endData = {
                id: `chatcmpl-${Date.now()}`,
                object: "chat.completion.chunk",
                created: Math.floor(Date.now() / 1000),
                model,
                choices: [
                    {
                        index: 0,
                        delta: {},
                        finish_reason: "stop"
                    }
                ]
            };
            res.write(`data: ${JSON.stringify(endData)}\n\n`);
            res.write("data: [DONE]\n\n");
            res.end();
        } else {
            // 非流式响应
            const response = await codeAssist.generateContent({
                model,
                contents,
                config: {
                    ...generationConfig,
                    systemInstruction
                }
            });

            const openaiResponse = {
                id: `chatcmpl-${Date.now()}`,
                object: "chat.completion",
                created: Math.floor(Date.now() / 1000),
                model,
                choices: [
                    {
                        index: 0,
                        message: {
                            role: "assistant",
                            content: response.candidates?.[0]?.content?.parts?.[0]?.text || ""
                        },
                        finish_reason: "stop"
                    }
                ],
                usage: {
                    prompt_tokens: 0, // Gemini API 不提供具体数值
                    completion_tokens: 0,
                    total_tokens: 0
                }
            };

            res.json(openaiResponse);
        }
    } catch (error: any) {
        console.error("OpenAI Compatible API Error:", error);
        res.status(500).json({
            error: {
                message: error.message || "Internal server error",
                type: "api_error"
            }
        });
    }
});

// 模型列表端点
app.get("/v1/models", async (req, res) => {
    const models = [
        {
            id: "gemini-2.5-flash",
            object: "model",
            created: Math.floor(Date.now() / 1000),
            owned_by: "google"
        },
        {
            id: "gemini-2.5-pro",
            object: "model",
            created: Math.floor(Date.now() / 1000),
            owned_by: "google"
        },
        {
            id: "gemini-1.5-flash",
            object: "model",
            created: Math.floor(Date.now() / 1000),
            owned_by: "google"
        },
        {
            id: "gemini-1.5-pro",
            object: "model",
            created: Math.floor(Date.now() / 1000),
            owned_by: "google"
        }
    ];

    res.json({
        object: "list",
        data: models
    });
});

// Request handler for Gemini API
app.all("/*", async (req, res) => {
    // 跳过配置API路径和OpenAI兼容路径
    if (req.path.startsWith("/api/config") || req.path.startsWith("/v1/")) {
        return res.status(404).json({ error: "Not found" });
    }

    const pathname = req.path;
    const [model, action] =
        pathname
            .split("/")
            .find((part) => part.includes(":"))
            ?.split(":") ?? [];

    if (!model || !action) {
        return res.status(400).json({ error: "Invalid request" });
    }

    const payload = req.body || {};

    // Uncomment for debugging
    // console.info("Request details:", { model, action, payload });

    const getGenerateContentParameters = () =>
        ({
            model,
            contents: payload.contents,
            config: {
                ...payload.generationConfig,
                tools: payload.tools,
                toolConfig: payload.toolConfig,
                safetySettings: payload.safetySettings,
                systemInstruction: payload.systemInstruction
            }
        } as const);

    try {
        switch (action) {
            case "generateContent": {
                const result = await codeAssist.generateContent(getGenerateContentParameters());
                return res.json(result);
            }
            case "streamGenerateContent": {
                const stream = await codeAssist.generateContentStream(getGenerateContentParameters());

                res.writeHead(200, {
                    "Content-Type": "text/event-stream",
                    "Cache-Control": "no-cache",
                    Connection: "keep-alive",
                    "Transfer-Encoding": "chunked",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "Cache-Control"
                });

                try {
                    for await (const chunk of stream) {
                        res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                    }
                    res.end();
                } catch (error) {
                    console.error("Error in SSE stream:", error);
                    res.end();
                }
                return;
            }

            case "countTokens": {
                const result = await codeAssist.countTokens({
                    model,
                    contents: payload.contents,
                    config: {
                        ...payload.generateContentRequest
                    }
                });
                return res.json(result);
            }
            case "embedContent": {
                const result = await codeAssist.embedContent({
                    model,
                    contents: payload.contents,
                    config: {
                        taskType: payload.taskType,
                        title: payload.title,
                        outputDimensionality: payload.outputDimensionality
                    }
                });
                return res.json(result);
            }
            default: {
                return res.status(400).json({ error: `Invalid action: ${action}` });
            }
        }
    } catch (error) {
        if (error instanceof GaxiosError && error.response) {
            return res.status(error.response.status).json(error.response.data);
        }
        console.error("Error processing request:", error);
        return res.status(500).json({ error: "Internal Server Error" });
    }
});

// Start server
async function startServer() {
    await initializeCodeAssist();

    app.listen(port, () => {
        console.log(`Server running on http://localhost:${port}`);
    });
}

startServer().catch(console.error);

// 保存当前登录信息
app.post("/api/config/save", async (req, res) => {
    try {
        const { accountName } = req.body;

        if (!accountName) {
            return res.status(400).json({
                success: false,
                error: "账户名称不能为空"
            });
        }

        // 检查是否有当前登录信息
        const hasDefaultCreds = await fs
            .access(DEFAULT_CREDS)
            .then(() => true)
            .catch(() => false);
        const projectId = env.GOOGLE_CLOUD_PROJECT;

        if (!hasDefaultCreds || !projectId) {
            return res.status(400).json({
                success: false,
                error: "当前没有有效的登录信息"
            });
        }

        // 创建目录
        await fs.mkdir(ACCOUNTS_DIR, { recursive: true });
        await fs.mkdir(PROJECTS_DIR, { recursive: true });

        // 保存凭证文件和项目ID
        const targetCreds = path.join(ACCOUNTS_DIR, `${accountName}.json`);
        const targetProject = path.join(PROJECTS_DIR, `${accountName}.txt`);

        await fs.copyFile(DEFAULT_CREDS, targetCreds);
        await fs.writeFile(targetProject, projectId);

        res.json({
            success: true,
            message: `账户 "${accountName}" 保存成功`,
            accountName,
            projectId
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "保存账户失败: " + error.message
        });
    }
});

// 清除当前登录信息
app.post("/api/config/clear", async (req, res) => {
    try {
        // 删除OAuth凭证文件
        await fs.unlink(DEFAULT_CREDS).catch(() => {});

        // 清除环境变量 (注意：这只影响当前进程)
        delete env.GOOGLE_CLOUD_PROJECT;
        delete env.GOOGLE_APPLICATION_CREDENTIALS;
        delete env.GEMINI_OAUTH_CREDS;
        delete env.GEMINI_PROJECT_ID;

        res.json({
            success: true,
            message: "登录信息已清除，请重启服务器以完全生效"
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "清除登录信息失败: " + error.message
        });
    }
});

// 获取已保存的账户列表
app.get("/api/config/accounts", async (req, res) => {
    try {
        const accounts = [];

        // 检查目录是否存在
        const hasAccountsDir = await fs
            .access(ACCOUNTS_DIR)
            .then(() => true)
            .catch(() => false);

        if (hasAccountsDir) {
            const files = await fs.readdir(ACCOUNTS_DIR);

            for (const file of files) {
                if (file.endsWith(".json")) {
                    const accountName = path.basename(file, ".json");
                    const projectFile = path.join(PROJECTS_DIR, `${accountName}.txt`);

                    try {
                        const projectId = await fs.readFile(projectFile, "utf-8");
                        accounts.push({
                            name: accountName,
                            projectId: projectId.trim(),
                            credsPath: path.join(ACCOUNTS_DIR, file)
                        });
                    } catch {
                        // 如果没有项目ID文件，跳过这个账户
                        continue;
                    }
                }
            }
        }

        res.json({
            success: true,
            accounts
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "获取账户列表失败: " + error.message
        });
    }
});

// 切换账户
app.post("/api/config/switch", async (req, res) => {
    try {
        const { accountName } = req.body;

        if (!accountName) {
            return res.status(400).json({
                success: false,
                error: "账户名称不能为空"
            });
        }

        if (accountName === "default") {
            // 切换到默认账户
            delete env.GOOGLE_APPLICATION_CREDENTIALS;
            delete env.GEMINI_OAUTH_CREDS;
            delete env.GEMINI_PROJECT_ID;

            res.json({
                success: true,
                message: "已切换到默认账户，请重启服务器以生效",
                needRestart: true
            });
        } else {
            // 切换到指定账户
            const targetCreds = path.join(ACCOUNTS_DIR, `${accountName}.json`);
            const targetProject = path.join(PROJECTS_DIR, `${accountName}.txt`);

            // 检查文件是否存在
            const hasCredsFile = await fs
                .access(targetCreds)
                .then(() => true)
                .catch(() => false);
            const hasProjectFile = await fs
                .access(targetProject)
                .then(() => true)
                .catch(() => false);

            if (!hasCredsFile || !hasProjectFile) {
                return res.status(404).json({
                    success: false,
                    error: "账户配置文件不存在"
                });
            }

            const projectId = await fs.readFile(targetProject, "utf-8");

            // 设置环境变量
            env.GEMINI_OAUTH_CREDS = targetCreds;
            env.GEMINI_PROJECT_ID = projectId.trim();

            res.json({
                success: true,
                message: `已切换到账户 "${accountName}"，请重启服务器以生效`,
                accountName,
                projectId: projectId.trim(),
                needRestart: true
            });
        }
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "切换账户失败: " + error.message
        });
    }
});

// 删除账户
app.delete("/api/config/accounts/:accountName", async (req, res) => {
    try {
        const { accountName } = req.params;

        if (!accountName || accountName === "default") {
            return res.status(400).json({
                success: false,
                error: "无效的账户名称"
            });
        }

        const targetCreds = path.join(ACCOUNTS_DIR, `${accountName}.json`);
        const targetProject = path.join(PROJECTS_DIR, `${accountName}.txt`);

        // 删除文件
        await fs.unlink(targetCreds).catch(() => {});
        await fs.unlink(targetProject).catch(() => {});

        res.json({
            success: true,
            message: `账户 "${accountName}" 已删除`
        });
    } catch (error: any) {
        res.status(500).json({
            success: false,
            error: "删除账户失败: " + error.message
        });
    }
});
