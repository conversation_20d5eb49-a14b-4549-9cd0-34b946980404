@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 启动 Gemini CLI Backend (编译模式 - Windows)
echo ===============================================
echo.

REM 设置代理环境变量
set https_proxy=http://127.0.0.1:7890
set http_proxy=http://127.0.0.1:7890
set all_proxy=socks5://127.0.0.1:7890

REM 动态检测项目ID
echo 🔍 检测项目ID配置...

REM 1. 优先使用环境变量中的项目ID
if defined GOOGLE_CLOUD_PROJECT (
    echo   ✅ 使用环境变量中的项目ID: %GOOGLE_CLOUD_PROJECT%
    goto :project_id_found
)

if defined GEMINI_PROJECT_ID (
    set GOOGLE_CLOUD_PROJECT=%GEMINI_PROJECT_ID%
    echo   ✅ 使用GEMINI_PROJECT_ID: %GEMINI_PROJECT_ID%
    goto :project_id_found
)

REM 2. 尝试从账户列表中读取第一个可用的项目ID
set ACCOUNTS_DIR=%USERPROFILE%\.gemini-accounts
set PROJECTS_DIR=%USERPROFILE%\.gemini-projects

if exist "%ACCOUNTS_DIR%" if exist "%PROJECTS_DIR%" (
    for %%f in ("%PROJECTS_DIR%\*.txt") do (
        if exist "%%f" (
            set /p PROJECT_ID=<"%%f"
            if not "!PROJECT_ID!"=="" (
                set GOOGLE_CLOUD_PROJECT=!PROJECT_ID!
                for %%n in ("%%f") do set ACCOUNT_NAME=%%~nn
                echo   ✅ 使用账户列表中的项目ID: !PROJECT_ID! ^(账户: !ACCOUNT_NAME!^)
                goto :project_id_found
            )
        )
    )
)

REM 3. 如果还是没有找到，提示用户配置
echo   ❌ 未找到项目ID配置
echo      💡 请通过以下方式配置项目ID:
echo         1. 设置环境变量: set GOOGLE_CLOUD_PROJECT=your-project-id
echo         2. 使用账户配置面板保存账户信息
echo         3. 运行 'gemini auth login' 设置默认账户
echo.
echo ❌ 启动失败: 缺少必要的项目ID配置
pause
exit /b 1

:project_id_found

echo.
echo 🌐 代理设置:
echo   GOOGLE_CLOUD_PROJECT=%GOOGLE_CLOUD_PROJECT%
echo   https_proxy=%https_proxy%
echo   http_proxy=%http_proxy%
echo   all_proxy=%all_proxy%
echo.

REM 检查OAuth认证配置
echo 🔐 OAuth认证检查:
if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo   ✅ 使用自定义凭证: %GOOGLE_APPLICATION_CREDENTIALS%
) else if defined GEMINI_OAUTH_CREDS (
    echo   ✅ 使用自定义凭证: %GEMINI_OAUTH_CREDS%
) else if exist "%USERPROFILE%\.gemini\oauth_creds.json" (
    echo   ✅ 使用默认凭证: %USERPROFILE%\.gemini\oauth_creds.json
) else (
    echo   ⚠️  未检测到OAuth凭证，服务器启动后将提示登录
    echo      💡 提示: 可以预先运行 'gemini auth login' 避免启动时登录
)
echo.

REM 编译TypeScript
echo 🔨 编译 TypeScript...
call npm run build

if errorlevel 1 (
    echo ❌ 编译失败，请检查错误信息
    pause
    exit /b 1
)

echo ✅ 编译成功
echo.
echo 🚀 启动服务器...
call npm start
