#!/bin/bash

echo "👥 Gemini 多账户管理工具"
echo "======================="
echo ""

# 定义账户配置目录
ACCOUNTS_DIR="$HOME/.gemini-accounts"
DEFAULT_CREDS="$HOME/.gemini/oauth_creds.json"

# 创建账户目录
mkdir -p "$ACCOUNTS_DIR"

# 显示当前状态
show_current_status() {
    echo "📊 当前认证状态:"
    echo "----------------"
    
    if [ -n "$GOOGLE_API_KEY" ]; then
        echo "✅ 当前使用: API Key 认证 (${GOOGLE_API_KEY:0:10}...)"
    elif [ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
        echo "✅ 当前使用: 自定义OAuth凭证 ($GOOGLE_APPLICATION_CREDENTIALS)"
    elif [ -n "$GEMINI_OAUTH_CREDS" ]; then
        echo "✅ 当前使用: 自定义OAuth凭证 ($GEMINI_OAUTH_CREDS)"
    elif [ -f "$DEFAULT_CREDS" ]; then
        echo "✅ 当前使用: 默认OAuth凭证 ($DEFAULT_CREDS)"
    else
        echo "❌ 未检测到有效认证"
    fi
    echo ""
}

# 列出所有账户
list_accounts() {
    echo "📋 已保存的账户:"
    echo "----------------"
    
    if [ -f "$DEFAULT_CREDS" ]; then
        echo "  default -> $DEFAULT_CREDS"
    fi
    
    if [ -d "$ACCOUNTS_DIR" ] && [ "$(ls -A $ACCOUNTS_DIR 2>/dev/null)" ]; then
        for file in "$ACCOUNTS_DIR"/*.json; do
            if [ -f "$file" ]; then
                account_name=$(basename "$file" .json)
                echo "  $account_name -> $file"
            fi
        done
    else
        echo "  (无已保存的账户)"
    fi
    echo ""
}

# 保存当前账户
save_current_account() {
    if [ ! -f "$DEFAULT_CREDS" ]; then
        echo "❌ 未找到默认OAuth凭证文件: $DEFAULT_CREDS"
        echo "请先运行 'gemini auth login' 进行认证"
        return 1
    fi
    
    read -p "请输入账户名称: " account_name
    
    if [ -z "$account_name" ]; then
        echo "❌ 账户名称不能为空"
        return 1
    fi
    
    target_file="$ACCOUNTS_DIR/${account_name}.json"
    
    if [ -f "$target_file" ]; then
        read -p "账户 '$account_name' 已存在，是否覆盖? (y/n): " overwrite
        if [ "$overwrite" != "y" ] && [ "$overwrite" != "Y" ]; then
            echo "❌ 操作已取消"
            return 1
        fi
    fi
    
    cp "$DEFAULT_CREDS" "$target_file"
    echo "✅ 账户 '$account_name' 已保存到: $target_file"
}

# 切换账户
switch_account() {
    list_accounts
    read -p "请输入要切换的账户名称 (或 'default'): " account_name
    
    if [ -z "$account_name" ]; then
        echo "❌ 账户名称不能为空"
        return 1
    fi
    
    if [ "$account_name" = "default" ]; then
        if [ -f "$DEFAULT_CREDS" ]; then
            unset GOOGLE_APPLICATION_CREDENTIALS
            unset GEMINI_OAUTH_CREDS
            echo "✅ 已切换到默认账户"
            echo "请重新启动服务器以生效"
        else
            echo "❌ 默认账户不存在"
        fi
        return
    fi
    
    target_file="$ACCOUNTS_DIR/${account_name}.json"
    
    if [ ! -f "$target_file" ]; then
        echo "❌ 账户 '$account_name' 不存在"
        return 1
    fi
    
    export GEMINI_OAUTH_CREDS="$target_file"
    echo "✅ 已切换到账户: $account_name"
    echo "凭证文件: $target_file"
    echo ""
    echo "🔧 要使切换生效，请:"
    echo "1. 在当前终端运行: export GEMINI_OAUTH_CREDS=\"$target_file\""
    echo "2. 或者重新启动服务器"
    echo ""
    echo "💡 要永久设置，请添加到 ~/.bashrc 或 ~/.zshrc:"
    echo "   echo 'export GEMINI_OAUTH_CREDS=\"$target_file\"' >> ~/.zshrc"
}

# 删除账户
delete_account() {
    list_accounts
    read -p "请输入要删除的账户名称: " account_name
    
    if [ -z "$account_name" ]; then
        echo "❌ 账户名称不能为空"
        return 1
    fi
    
    if [ "$account_name" = "default" ]; then
        echo "❌ 不能删除默认账户"
        return 1
    fi
    
    target_file="$ACCOUNTS_DIR/${account_name}.json"
    
    if [ ! -f "$target_file" ]; then
        echo "❌ 账户 '$account_name' 不存在"
        return 1
    fi
    
    read -p "确认删除账户 '$account_name'? (y/n): " confirm
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
        rm "$target_file"
        echo "✅ 账户 '$account_name' 已删除"
    else
        echo "❌ 操作已取消"
    fi
}

# 主菜单
show_menu() {
    echo "请选择操作:"
    echo "1. 显示当前状态"
    echo "2. 列出所有账户"
    echo "3. 保存当前账户"
    echo "4. 切换账户"
    echo "5. 删除账户"
    echo "6. 退出"
    echo ""
}

# 主循环
while true; do
    show_current_status
    show_menu
    read -p "请输入选项 (1-6): " choice
    echo ""
    
    case $choice in
        1)
            show_current_status
            ;;
        2)
            list_accounts
            ;;
        3)
            save_current_account
            ;;
        4)
            switch_account
            ;;
        5)
            delete_account
            ;;
        6)
            echo "👋 再见!"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请输入 1-6"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
    echo ""
done
