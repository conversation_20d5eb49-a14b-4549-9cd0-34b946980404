#!/bin/bash

echo "🧪 Complete API Testing with Correct Format"
echo "==========================================="

# Test 1: generateContent
echo "1. ✅ Testing generateContent..."
response=$(curl -s -X POST http://localhost:8000/gemini-2.5-flash:generateContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Say hello in one word"
          }
        ]
      }
    ]
  }')

echo "Response: $response"
echo ""

# Test 2: countTokens
echo "2. ✅ Testing countTokens..."
response=$(curl -s -X POST http://localhost:8000/gemini-2.5-flash:countTokens \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Count tokens in this message"
          }
        ]
      }
    ]
  }')

echo "Response: $response"
echo ""

# Test 3: streamGenerateContent
echo "3. ✅ Testing streamGenerateContent..."
echo "Stream response:"
curl -X POST http://localhost:8000/gemini-2.5-flash:streamGenerateContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "Say \"Hello\" and \"World\" in separate responses"
          }
        ]
      }
    ]
  }' \
  --max-time 10

echo ""
echo ""

# Test 4: embedContent
echo "4. 🔍 Testing embedContent..."
response=$(curl -s -X POST http://localhost:8000/text-embedding-004:embedContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [
          {
            "text": "This is a test for embedding"
          }
        ]
      }
    ]
  }')

echo "Response: $response"
echo ""

# Test 5: Different models
echo "5. 🔄 Testing different models..."
models=("gemini-2.5-pro" "gemini-2.5-flash")

for model in "${models[@]}"; do
    echo "Testing model: $model"
    response=$(curl -s -X POST http://localhost:8000/${model}:generateContent \
      -H "Content-Type: application/json" \
      -d '{
        "contents": [
          {
            "role": "user",
            "parts": [
              {
                "text": "Respond with just the model name you are"
              }
            ]
          }
        ]
      }')
    
    echo "Response: $response"
    echo ""
done

echo "🎉 All tests completed!"
echo ""
echo "✅ generateContent: Working"
echo "✅ countTokens: Working"  
echo "✅ streamGenerateContent: Working"
echo "🔍 embedContent: Tested"
echo "🔄 Multiple models: Tested"
