#!/bin/bash

# 设置代理环境变量
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890

# 检查项目ID配置
if [ -z "$GOOGLE_CLOUD_PROJECT" ]; then
    echo "❌ 错误: 未设置 GOOGLE_CLOUD_PROJECT 环境变量"
    echo "请先设置项目ID:"
    echo "  export GOOGLE_CLOUD_PROJECT=your-project-id"
    echo "或者使用主启动脚本: ../../start.sh"
    exit 1
fi

echo "Environment variables set:"
echo "GOOGLE_CLOUD_PROJECT=$GOOGLE_CLOUD_PROJECT"
echo "https_proxy=$https_proxy"
echo "http_proxy=$http_proxy"
echo "all_proxy=$all_proxy"
echo ""

# 构建项目
echo "Building TypeScript..."
npm run build

if [ $? -eq 0 ]; then
    echo "Build successful. Starting server..."
    npm start
else
    echo "Build failed. Please check the errors above."
    exit 1
fi
