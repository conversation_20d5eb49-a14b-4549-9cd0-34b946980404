#!/bin/bash

echo "🔄 Detailed Streaming Test"
echo "========================="

# Function to test streaming with detailed output
test_streaming() {
    local description="$1"
    local url="$2"
    
    echo ""
    echo "Testing: $description"
    echo "URL: $url"
    echo "----------------------------------------"
    
    # Test with curl showing timing and headers
    echo "📊 Response headers and timing:"
    curl -v -X POST "$url" \
        -H "Content-Type: application/json" \
        -d '{
            "contents": [
                {
                    "role": "user", 
                    "parts": [
                        {
                            "text": "Please count from 1 to 5, saying each number in a separate chunk. Be verbose and explain what you are doing."
                        }
                    ]
                }
            ]
        }' \
        --max-time 30 \
        -w "\n\nTiming Info:\n============\nTotal time: %{time_total}s\nTime to first byte: %{time_starttransfer}s\nConnect time: %{time_connect}s\n" \
        2>&1
    
    echo ""
    echo "----------------------------------------"
}

# Test current Node.js implementation
echo "🟢 Testing Current Node.js Implementation (Express-based)"
test_streaming "Node.js Express SSE" "http://localhost:8000/gemini-2.5-flash:streamGenerateContent"

echo ""
echo ""
echo "🔍 Analysis:"
echo "============"
echo "1. Check if chunks arrive incrementally (not all at once)"
echo "2. Verify proper SSE format (data: prefix, double newlines)"
echo "3. Check timing - first byte should arrive quickly"
echo "4. Verify connection stays open during streaming"
echo ""

# Test with a longer prompt to see streaming behavior
echo "🔄 Extended Streaming Test (longer response)"
echo "============================================="

curl -X POST "http://localhost:8000/gemini-2.5-flash:streamGenerateContent" \
    -H "Content-Type: application/json" \
    -d '{
        "contents": [
            {
                "role": "user",
                "parts": [
                    {
                        "text": "Write a short story about a robot learning to paint. Make it at least 3 paragraphs long and stream each sentence separately."
                    }
                ]
            }
        ]
    }' \
    --max-time 60 \
    -w "\nTotal time: %{time_total}s\n"

echo ""
echo "🎯 Test completed!"
