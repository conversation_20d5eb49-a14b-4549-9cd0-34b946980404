#!/bin/bash

echo "🚀 启动 Gemini CLI Backend (编译模式)"
echo "=================================="

# 设置代理环境变量
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890

# 动态检测项目ID
echo "🔍 检测项目ID配置..."

# 1. 优先使用环境变量中的项目ID
if [ -n "$GOOGLE_CLOUD_PROJECT" ]; then
    echo "  ✅ 使用环境变量中的项目ID: $GOOGLE_CLOUD_PROJECT"
elif [ -n "$GEMINI_PROJECT_ID" ]; then
    export GOOGLE_CLOUD_PROJECT="$GEMINI_PROJECT_ID"
    echo "  ✅ 使用GEMINI_PROJECT_ID: $GEMINI_PROJECT_ID"
else
    # 2. 尝试从账户列表中读取第一个可用的项目ID
    ACCOUNTS_DIR="$HOME/.gemini-accounts"
    PROJECTS_DIR="$HOME/.gemini-projects"

    if [ -d "$ACCOUNTS_DIR" ] && [ -d "$PROJECTS_DIR" ]; then
        # 查找第一个可用的项目ID文件
        for project_file in "$PROJECTS_DIR"/*.txt; do
            if [ -f "$project_file" ]; then
                PROJECT_ID=$(cat "$project_file" 2>/dev/null | tr -d '\n\r' | xargs)
                if [ -n "$PROJECT_ID" ]; then
                    export GOOGLE_CLOUD_PROJECT="$PROJECT_ID"
                    ACCOUNT_NAME=$(basename "$project_file" .txt)
                    echo "  ✅ 使用账户列表中的项目ID: $PROJECT_ID (账户: $ACCOUNT_NAME)"
                    break
                fi
            fi
        done
    fi

    # 3. 如果还是没有找到，提示用户配置
    if [ -z "$GOOGLE_CLOUD_PROJECT" ]; then
        echo "  ❌ 未找到项目ID配置"
        echo "     💡 请通过以下方式配置项目ID:"
        echo "        1. 设置环境变量: export GOOGLE_CLOUD_PROJECT=your-project-id"
        echo "        2. 使用账户配置面板保存账户信息"
        echo "        3. 运行 'gemini auth login' 设置默认账户"
        echo ""
        echo "❌ 启动失败: 缺少必要的项目ID配置"
        exit 1
    fi
fi

echo "🌐 代理设置:"
echo "  GOOGLE_CLOUD_PROJECT=$GOOGLE_CLOUD_PROJECT"
echo "  https_proxy=$https_proxy"
echo "  http_proxy=$http_proxy"
echo "  all_proxy=$all_proxy"
echo ""

# 检查OAuth认证配置
echo "🔐 OAuth认证检查:"
if [ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
    echo "  ✅ 使用自定义凭证: $GOOGLE_APPLICATION_CREDENTIALS"
elif [ -n "$GEMINI_OAUTH_CREDS" ]; then
    echo "  ✅ 使用自定义凭证: $GEMINI_OAUTH_CREDS"
elif [ -f "$HOME/.gemini/oauth_creds.json" ]; then
    echo "  ✅ 使用默认凭证: ~/.gemini/oauth_creds.json"
else
    echo "  ⚠️  未检测到OAuth凭证，服务器启动后将提示登录"
    echo "     💡 提示: 可以预先运行 'gemini auth login' 避免启动时登录"
fi
echo ""

# 编译TypeScript
echo "🔨 编译 TypeScript..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 编译成功"
    echo ""
    echo "🚀 启动服务器..."
    npm start
else
    echo "❌ 编译失败，请检查错误信息"
    exit 1
fi
