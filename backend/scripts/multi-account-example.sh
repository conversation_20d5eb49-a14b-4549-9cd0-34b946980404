#!/bin/bash

echo "👥 Google账户多账户管理示例"
echo "=========================="
echo ""
echo "💡 仅支持Google个人账户认证，通过不同的OAuth凭证文件实现多账户切换"
echo ""

echo "📚 使用场景示例:"
echo ""

echo "🔹 场景1: 个人开发 + 工作项目"
echo "--------------------------------"
echo "# 个人账户"
echo "export GEMINI_OAUTH_CREDS=~/.gemini-accounts/personal.json"
echo "npm run dev"
echo ""
echo "# 工作账户"
echo "export GEMINI_OAUTH_CREDS=~/.gemini-accounts/work.json"
echo "npm run dev"
echo ""

echo "🔹 场景2: 多个客户项目"
echo "----------------------"
echo "# 客户A项目"
echo "export GEMINI_OAUTH_CREDS=~/.gemini-accounts/client-a.json"
echo "npm run dev"
echo ""
echo "# 客户B项目"
echo "export GEMINI_OAUTH_CREDS=~/.gemini-accounts/client-b.json"
echo "npm run dev"
echo ""

echo "🔹 场景3: 开发/测试/生产环境"
echo "----------------------------"
echo "# 开发环境 (个人账户)"
echo "export GEMINI_OAUTH_CREDS=~/.gemini-accounts/dev.json"
echo ""
echo "# 测试环境 (测试账户)"
echo "export GEMINI_OAUTH_CREDS=~/.gemini-accounts/test.json"
echo ""
echo "# 生产环境 (生产账户)"
echo "export GEMINI_OAUTH_CREDS=~/.gemini-accounts/prod.json"
echo ""

echo "📋 设置步骤:"
echo "============"
echo ""

echo "1️⃣ 为每个账户进行认证:"
echo "   gemini auth login"
echo "   # 登录第一个账户后，保存凭证"
echo "   cp ~/.gemini/oauth_creds.json ~/.gemini-accounts/account1.json"
echo ""
echo "   # 清除当前凭证，登录第二个账户"
echo "   rm ~/.gemini/oauth_creds.json"
echo "   gemini auth login"
echo "   cp ~/.gemini/oauth_creds.json ~/.gemini-accounts/account2.json"
echo ""

echo "2️⃣ 使用多账户管理工具:"
echo "   ./scripts/manage-accounts.sh"
echo ""

echo "3️⃣ 在项目中切换账户:"
echo "   # 方式1: 临时切换"
echo "   GEMINI_OAUTH_CREDS=~/.gemini-accounts/work.json npm run dev"
echo ""
echo "   # 方式2: 环境变量"
echo "   export GEMINI_OAUTH_CREDS=~/.gemini-accounts/work.json"
echo "   npm run dev"
echo ""
echo "   # 方式3: 添加到shell配置"
echo "   echo 'export GEMINI_OAUTH_CREDS=~/.gemini-accounts/work.json' >> ~/.zshrc"
echo ""

echo "🎯 最佳实践:"
echo "============"
echo ""

echo "📁 推荐的目录结构:"
echo "~/.gemini-accounts/"
echo "├── personal.json      # 个人账户"
echo "├── work.json         # 工作账户"
echo "├── client-a.json     # 客户A"
echo "├── client-b.json     # 客户B"
echo "├── dev.json          # 开发环境"
echo "├── test.json         # 测试环境"
echo "└── prod.json         # 生产环境"
echo ""

echo "🔒 安全建议:"
echo "- 定期轮换OAuth凭证"
echo "- 不要将凭证文件提交到版本控制"
echo "- 为不同项目使用不同的Google账户"
echo "- 生产环境建议使用Service Account"
echo ""

echo "🚀 快速开始:"
echo "============"
echo ""

read -p "是否要运行多账户管理工具? (y/n): " run_tool

if [ "$run_tool" = "y" ] || [ "$run_tool" = "Y" ]; then
    echo ""
    echo "🔧 启动多账户管理工具..."
    ./scripts/manage-accounts.sh
else
    echo ""
    echo "💡 提示: 随时可以运行 ./scripts/manage-accounts.sh 来管理账户"
fi

echo ""
echo "✨ 多账户配置完成!"
