#!/bin/bash

echo "Detailed API Testing..."
echo "======================"

# Test different model names
models=("gemini-1.5-flash" "gemini-1.5-pro" "gemini-pro")

for model in "${models[@]}"; do
    echo "Testing model: $model"
    echo "------------------------"
    
    # Test generateContent
    echo "Testing generateContent..."
    response=$(curl -s -X POST http://localhost:8000/${model}:generateContent \
      -H "Content-Type: application/json" \
      -d '{
        "contents": [
          {
            "parts": [
              {
                "text": "Say hello in one word"
              }
            ]
          }
        ]
      }')
    
    echo "Response: $response"
    echo ""
    
    # Test countTokens
    echo "Testing countTokens..."
    response=$(curl -s -X POST http://localhost:8000/${model}:countTokens \
      -H "Content-Type: application/json" \
      -d '{
        "contents": [
          {
            "parts": [
              {
                "text": "Count tokens"
              }
            ]
          }
        ]
      }')
    
    echo "Response: $response"
    echo ""
    echo "========================"
done

# Test streamGenerateContent
echo "Testing streamGenerateContent..."
curl -X POST http://localhost:8000/gemini-1.5-flash:streamGenerateContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Count from 1 to 3"
          }
        ]
      }
    ]
  }' \
  --max-time 10

echo ""
echo "Testing completed!"
