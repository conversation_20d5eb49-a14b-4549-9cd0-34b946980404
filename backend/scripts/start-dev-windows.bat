@echo off
setlocal enabledelayedexpansion

echo Gemini CLI Backend (Development Mode - Windows)
echo ===============================================
echo.

REM Set proxy environment variables
set https_proxy=http://127.0.0.1:7890
set http_proxy=http://127.0.0.1:7890
set all_proxy=socks5://127.0.0.1:7890

REM Detect project ID configuration
echo Detecting project ID configuration...

REM 1. Check environment variables for project ID
if defined GOOGLE_CLOUD_PROJECT (
    echo   [OK] Using project ID from environment: %GOOGLE_CLOUD_PROJECT%
    goto :project_id_found
)

if defined GEMINI_PROJECT_ID (
    set GOOGLE_CLOUD_PROJECT=%GEMINI_PROJECT_ID%
    echo   [OK] Using GEMINI_PROJECT_ID: %GEMINI_PROJECT_ID%
    goto :project_id_found
)

REM 2. Try to read from account list
set ACCOUNTS_DIR=%USERPROFILE%\.gemini-accounts
set PROJECTS_DIR=%USERPROFILE%\.gemini-projects

if exist "%ACCOUNTS_DIR%" if exist "%PROJECTS_DIR%" (
    for %%f in ("%PROJECTS_DIR%\*.txt") do (
        if exist "%%f" (
            set /p PROJECT_ID=<"%%f"
            if not "!PROJECT_ID!"=="" (
                set GOOGLE_CLOUD_PROJECT=!PROJECT_ID!
                for %%n in ("%%f") do set ACCOUNT_NAME=%%~nn
                echo   [OK] Using project ID from account list: !PROJECT_ID! (Account: !ACCOUNT_NAME!)
                goto :project_id_found
            )
        )
    )
)

REM 3. If still not found, show error
echo   [ERROR] Project ID configuration not found
echo      Please configure project ID using one of these methods:
echo         1. Set environment variable: set GOOGLE_CLOUD_PROJECT=your-project-id
echo         2. Use account configuration panel to save account info
echo         3. Run 'gemini auth login' to set up default account
echo.
echo [ERROR] Startup failed: Missing required project ID configuration
pause
exit /b 1

:project_id_found

echo.
echo Proxy settings:
echo   GOOGLE_CLOUD_PROJECT=%GOOGLE_CLOUD_PROJECT%
echo   https_proxy=%https_proxy%
echo   http_proxy=%http_proxy%
echo   all_proxy=%all_proxy%
echo.

REM Check OAuth authentication configuration
echo OAuth authentication check:
if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo   [OK] Using custom credentials: %GOOGLE_APPLICATION_CREDENTIALS%
) else if defined GEMINI_OAUTH_CREDS (
    echo   [OK] Using custom credentials: %GEMINI_OAUTH_CREDS%
) else if exist "%USERPROFILE%\.gemini\oauth_creds.json" (
    echo   [OK] Using default credentials: %USERPROFILE%\.gemini\oauth_creds.json
) else (
    echo   [WARNING] No OAuth credentials detected, server will prompt for login after startup
    echo      Tip: You can run 'gemini auth login' beforehand to avoid login prompt
)
echo.

REM Build TypeScript
echo Building TypeScript...
call npm run build

if errorlevel 1 (
    echo [ERROR] Build failed, please check error messages above
    pause
    exit /b 1
)

echo [OK] Build successful
echo.
echo Starting server...
call npm start
