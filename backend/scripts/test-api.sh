#!/bin/bash

echo "Testing Gemini CLI API endpoints..."
echo "=================================="

# Test 1: Basic health check
echo "1. Testing basic connectivity..."
curl -s -o /dev/null -w "HTTP Status: %{http_code}\n" http://localhost:8000/

echo ""

# Test 2: Test generateContent endpoint
echo "2. Testing generateContent endpoint..."
curl -X POST http://localhost:8000/gemini-1.5-flash:generateContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Hello, how are you?"
          }
        ]
      }
    ]
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""

# Test 3: Test countTokens endpoint
echo "3. Testing countTokens endpoint..."
curl -X POST http://localhost:8000/gemini-1.5-flash:countTokens \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "parts": [
          {
            "text": "Count the tokens in this text"
          }
        ]
      }
    ]
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""

# Test 4: Test invalid endpoint
echo "4. Testing invalid endpoint (should return 400)..."
curl -X POST http://localhost:8000/invalid-endpoint \
  -H "Content-Type: application/json" \
  -d '{}' \
  -w "\nHTTP Status: %{http_code}\n"

echo ""
echo "API testing completed!"
