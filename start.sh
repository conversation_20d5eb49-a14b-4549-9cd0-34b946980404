#!/bin/bash

echo "🚀 启动 Gemini CLI Chat 全栈应用"
echo "=================================="

# 检查是否在正确的目录
if [ ! -d "backend" ] || [ ! -d "web" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查OAuth认证
if [ ! -f "$HOME/.gemini/oauth_creds.json" ] && [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ] && [ -z "$GEMINI_OAUTH_CREDS" ]; then
    echo "⚠️  警告: 未检测到OAuth认证配置"
    echo "请执行以下操作之一:"
    echo "1. 运行 'gemini auth login' 设置默认账户 (推荐)"
    echo "2. 设置 'export GEMINI_OAUTH_CREDS=/path/to/oauth_creds.json' (多账户)"
    echo "3. 使用多账户管理工具: cd backend && ./scripts/manage-accounts.sh"
    exit 1
else
    echo "✅ OAuth认证配置检查通过"
fi

echo "✅ 环境检查通过"
echo ""

# 启动后端
echo "🔧 启动后端服务..."
cd backend
chmod +x scripts/start-dev.sh
./scripts/start-dev.sh &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 8

# 检查后端是否启动成功
echo "🔍 检查后端服务状态..."
for i in {1..10}; do
    if curl -s http://localhost:8000 > /dev/null 2>&1; then
        echo "✅ 后端服务启动成功 (http://localhost:8000)"
        break
    else
        if [ $i -eq 10 ]; then
            echo "❌ 后端服务启动失败或超时"
            kill $BACKEND_PID 2>/dev/null
            exit 1
        fi
        echo "⏳ 等待后端服务... ($i/10)"
        sleep 2
    fi
done

echo ""

# 启动前端
echo "🎨 启动前端服务..."
cd web
npm run dev &
FRONTEND_PID=$!
cd ..

# 等待前端启动
echo "⏳ 等待前端服务启动..."
sleep 3

# 检查前端是否启动成功
if curl -s http://localhost:9000 > /dev/null; then
    echo "✅ 前端服务启动成功 (http://localhost:9000)"
else
    echo "❌ 前端服务启动失败"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🎉 全栈应用启动成功！"
echo "=================================="
echo "📱 前端界面: http://localhost:9000"
echo "🔌 后端API:  http://localhost:8000"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo "✅ 所有服务已停止"; exit 0' INT

# 保持脚本运行
wait
