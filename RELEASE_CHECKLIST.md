# 📦 发布清单

## 🔍 发布前检查

### 1. 代码清理
- [ ] 移除所有硬编码的项目 ID 和敏感信息
- [ ] 清理调试日志和临时文件
- [ ] 确保所有脚本有执行权限
- [ ] 检查 `.gitignore` 文件完整性

### 2. 文档更新
- [ ] 更新 README.md (使用 README_NEW.md)
- [ ] 确保 INSTALLATION.md 完整准确
- [ ] 更新 package.json 版本号
- [ ] 添加 CHANGELOG.md (如果需要)

### 3. 功能测试
- [ ] 测试自动安装脚本 `./setup.sh`
- [ ] 测试启动脚本 `./start.sh`
- [ ] 测试 Web 界面功能
- [ ] 测试 API 接口
- [ ] 测试 OpenAI 兼容接口
- [ ] 测试多账户管理

### 4. 环境测试
- [ ] 在干净的环境中测试安装
- [ ] 测试不同 Node.js 版本兼容性
- [ ] 测试代理配置功能
- [ ] 测试错误处理和提示

## 📋 发布准备

### 1. 清理项目
```bash
# 清理构建文件
rm -rf backend/dist
rm -rf backend/node_modules
rm -rf web/node_modules
rm -rf node_modules

# 清理临时文件
rm -rf temp/
rm -rf .DS_Store
find . -name "*.log" -delete
```

### 2. 更新文档
```bash
# 替换 README
mv README.md README_OLD.md
mv README_NEW.md README.md

# 确保脚本可执行
chmod +x setup.sh
chmod +x start.sh
chmod +x start-stable.sh
chmod +x quick-start.sh
chmod +x backend/scripts/*.sh
```

### 3. 版本管理
```bash
# 更新版本号 (在 package.json 中)
# 创建 git tag
git tag -a v1.0.0 -m "Release version 1.0.0"
```

## 🚀 发布方式

### 方式一: GitHub Release

1. **创建 Repository**
   ```bash
   # 在 GitHub 创建新仓库
   # 推送代码
   git remote add origin https://github.com/your-username/gemini-cli-chat.git
   git push -u origin main
   git push --tags
   ```

2. **创建 Release**
   - 在 GitHub 上创建新的 Release
   - 选择刚创建的 tag
   - 添加发布说明
   - 上传压缩包 (可选)

### 方式二: 直接分发

1. **创建分发包**
   ```bash
   # 创建压缩包
   tar -czf gemini-cli-chat-v1.0.0.tar.gz \
     --exclude=node_modules \
     --exclude=.git \
     --exclude=backend/dist \
     --exclude=temp \
     .
   ```

2. **分发方式**
   - 通过邮件发送
   - 上传到文件共享服务
   - 放在内部服务器

### 方式三: npm 包 (高级)

如果想发布为 npm 包:
```bash
# 登录 npm
npm login

# 发布
npm publish
```

## 📝 用户使用流程

### 新用户安装流程
1. 下载/克隆项目
2. 运行 `./setup.sh`
3. 按照提示配置 Google Cloud 认证
4. 运行 `./start.sh`
5. 访问 http://localhost:9000

### 配置要求
- Node.js 18+
- Google Cloud 项目
- OAuth 2.0 凭证
- 网络连接 (可能需要代理)

## 🔧 支持和维护

### 常见问题准备
- 认证配置问题
- 网络连接问题
- 端口占用问题
- 依赖安装问题

### 文档维护
- 保持 INSTALLATION.md 更新
- 收集用户反馈
- 更新故障排除指南

### 版本更新
- 定期更新依赖
- 跟进 Gemini API 变化
- 修复已知问题

## 📊 发布后跟踪

### 收集反馈
- GitHub Issues
- 用户使用报告
- 功能请求

### 性能监控
- API 响应时间
- 错误率统计
- 用户活跃度

### 持续改进
- 代码优化
- 功能增强
- 文档完善

## ✅ 发布检查清单

发布前确保以下项目都已完成:

- [ ] 代码清理完成
- [ ] 文档更新完成
- [ ] 功能测试通过
- [ ] 安装脚本测试通过
- [ ] 版本号已更新
- [ ] Git 标签已创建
- [ ] README 已更新
- [ ] 敏感信息已移除
- [ ] 脚本权限已设置
- [ ] 发布说明已准备

完成所有检查后，即可进行发布！
