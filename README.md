# 🤖 Gemini CLI Chat

一个功能完整的 Gemini AI 聊天界面，支持多种模型和高级功能。

## 📁 项目结构

```
gemini-cli/
├── backend/          # 后端API服务
│   ├── src/         # TypeScript源代码
│   ├── dist/        # 编译后的JavaScript
│   └── *.sh         # 启动脚本
├── web/             # 前端界面
│   ├── index.html   # 主页面
│   ├── main.js      # 主要逻辑
│   └── style.css    # 样式文件
├── docs/            # 项目文档
├── scripts/         # 通用脚本
└── temp/            # 临时测试文件
```

## 🚀 快速启动

### 首次使用

```bash
# 1. 设置OAuth认证
gemini auth login

# 2. 一键启动全栈应用
./start.sh
```

### 日常使用

```bash
# 方式1: 稳定启动 (推荐 - 编译模式)
./start-stable.sh

# 方式2: 开发启动 (ts-node模式)
./start.sh

# 方式3: 快速启动
./quick-start.sh

# 方式4: 分别启动
cd backend && ./scripts/start-dev.sh  # 后端
cd web && npm run dev                 # 前端 (新终端)
```

### 访问应用

-   前端: http://localhost:9000
-   后端 API: http://localhost:8000

## ✨ 主要功能

### 🤖 **支持的模型**

-   **Gemini 2.5**: flash, pro, flash-lite
-   **Gemini 2.0**: flash, flash-live
-   **Gemini 1.5**: flash, pro
-   **LearnLM**: 教育专用模型

### 💬 **对话功能**

-   流式对话 (实时显示)
-   普通对话 (一次性显示)
-   逐字打字效果
-   可调节打字速度
-   对话中止功能

### 🛠️ **工具功能**

-   Token 计数
-   内容嵌入
-   模型切换
-   对话导出

## 📚 文档

详细文档请查看 `docs/` 目录：

-   [模型测试报告](docs/MODEL_TEST_REPORT.md)
-   [流式对话对比](docs/STREAMING_COMPARISON_REPORT.md)

## 🔧 环境要求

-   Node.js >= 18.0.0
-   Google 个人账户 OAuth 认证
-   网络代理配置 (如需要)

## ⚠️ 重要说明

-   **浏览器测试**: 此项目不使用 Playwright 自动化测试，请手动在浏览器中测试功能
-   **多项目兼容**: 避免与其他使用 Playwright 的项目冲突

## 📝 开发说明

项目采用前后端分离架构：

-   **后端**: Express + TypeScript + Gemini API
-   **前端**: Vanilla JS + Vite + 现代 CSS

## 🎯 特色功能

-   ⌨️ **真正的打字效果**: 逐字显示，可调速度
-   ⏹️ **智能中止**: 流式对话可随时停止
-   🎨 **现代界面**: 响应式设计，用户友好
-   🔄 **多模型支持**: 一键切换不同 Gemini 模型

---

开始使用 Gemini CLI Chat，体验最先进的 AI 对话！
