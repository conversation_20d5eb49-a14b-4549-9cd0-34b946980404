# 🔌 Roo Code 集成指南

本文档说明如何将 Gemini CLI Chat API 接入到 Roo Code 中使用。

## 📋 概述

Gemini CLI Chat 现在支持 OpenAI 兼容的 API 端点，可以直接集成到 Roo Code 中作为自定义 AI 提供商使用。

## 🛠️ 配置步骤

### 1. 启动 Gemini CLI Chat 服务

```bash
# 确保项目ID已配置
export GOOGLE_CLOUD_PROJECT=your-project-id

# 启动服务
./start.sh
```

服务将在以下端点运行：
- **前端界面**: http://localhost:9000
- **后端API**: http://localhost:8000
- **OpenAI兼容API**: http://localhost:8000/v1

### 2. 在 Roo Code 中配置

#### 步骤 1: 打开 Roo Code 设置
1. 在 VS Code 中打开 Roo Code 侧边栏
2. 点击设置图标（齿轮图标）

#### 步骤 2: 选择 OpenAI Compatible 提供商
1. 在 "API Provider" 下拉菜单中选择 **"OpenAI Compatible"**
2. 配置以下设置：

```
API Provider: OpenAI Compatible
Base URL: http://localhost:8000/v1
API Key: any-key-works (本地服务不需要真实密钥)
Model: gemini-2.5-flash
```

#### 步骤 3: 高级配置（可选）
```
Max Output Tokens: 8192
Context Window: 32768
Image Support: false
Computer Use: false
Input Price: 0
Output Price: 0
```

### 3. 可用模型

| 模型ID | 描述 | 推荐用途 |
|--------|------|----------|
| `gemini-2.5-flash` | 最新快速模型 | 代码生成、日常对话 |
| `gemini-2.5-pro` | 高性能模型 | 复杂推理、架构设计 |
| `gemini-1.5-flash` | 稳定快速模型 | 代码审查、文档生成 |
| `gemini-1.5-pro` | 稳定高性能模型 | 深度分析、重构 |

## 🔧 API 端点说明

### Chat Completions
```
POST http://localhost:8000/v1/chat/completions
```

**请求格式**:
```json
{
  "model": "gemini-2.5-flash",
  "messages": [
    {"role": "user", "content": "Hello, world!"}
  ],
  "stream": false,
  "max_tokens": 8192,
  "temperature": 0.7
}
```

**响应格式**:
```json
{
  "id": "chatcmpl-1234567890",
  "object": "chat.completion",
  "created": 1234567890,
  "model": "gemini-2.5-flash",
  "choices": [{
    "index": 0,
    "message": {
      "role": "assistant",
      "content": "Hello! How can I help you today?"
    },
    "finish_reason": "stop"
  }],
  "usage": {
    "prompt_tokens": 0,
    "completion_tokens": 0,
    "total_tokens": 0
  }
}
```

### 模型列表
```
GET http://localhost:8000/v1/models
```

## 🎯 使用场景

### 1. 代码生成和重构
- 利用 Gemini 的代码理解能力
- 支持多种编程语言
- 智能代码补全和优化建议

### 2. 文档生成
- 自动生成 API 文档
- 代码注释和说明
- README 和技术文档编写

### 3. 代码审查
- 代码质量分析
- 安全漏洞检测
- 性能优化建议

### 4. 架构设计
- 系统架构规划
- 技术选型建议
- 最佳实践指导

## 🔍 故障排除

### 连接问题
1. **确保服务正在运行**:
   ```bash
   curl http://localhost:8000/v1/models
   ```

2. **检查端口占用**:
   ```bash
   lsof -i :8000
   ```

3. **查看服务日志**:
   检查启动脚本的输出日志

### 配置问题
1. **Base URL 错误**: 确保使用 `http://localhost:8000/v1`
2. **模型不存在**: 使用支持的模型ID
3. **API Key**: 本地服务可以使用任意字符串

### 性能优化
1. **选择合适的模型**: Flash 模型更快，Pro 模型更准确
2. **调整 temperature**: 代码生成建议使用较低值 (0.1-0.3)
3. **限制 max_tokens**: 根据需要设置合理的输出长度

## 📚 更多资源

- [Roo Code 官方文档](https://docs.roocode.com/)
- [OpenAI API 兼容性说明](https://docs.roocode.com/providers/openai-compatible)
- [Gemini API 文档](https://ai.google.dev/docs)

## 🤝 贡献

如果你在集成过程中遇到问题或有改进建议，欢迎：
1. 提交 Issue
2. 发起 Pull Request
3. 参与讨论

---

**注意**: 这是一个本地集成方案，适用于开发和测试环境。生产环境使用请确保适当的安全配置。
