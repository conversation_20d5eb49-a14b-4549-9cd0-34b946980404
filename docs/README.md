# Gemini CLI Node.js API Server

这是从 Deno 版本转换而来的 Node.js 版本的 Gemini CLI API 服务器。

## 项目结构

```
├── src/
│   └── code-assist-api.ts    # 主要的API服务器代码 (Node.js版本)
├── dist/                     # 编译后的JavaScript文件
├── code-assist-api.ts        # 原始Deno版本 (参考用)
├── code-assist-api2.ts       # 修改后的Deno版本 (参考用)
├── package.json              # Node.js项目配置
├── tsconfig.json            # TypeScript配置
├── start.sh                 # 生产环境启动脚本
├── start-dev.sh             # 开发环境启动脚本
├── test-api.sh              # 基本API测试脚本
├── test-detailed.sh         # 详细API测试脚本
├── test-complete.sh         # 完整功能测试脚本
├── test-streaming.sh        # 流式传输专项测试脚本
├── STREAMING_COMPARISON_REPORT.md  # 流式传输对比分析报告
└── README.md                # 项目说明文档
```

## 环境变量设置

在运行服务器之前，需要配置以下环境变量：

```bash
# 必需: 设置你的Google Cloud项目ID
export GOOGLE_CLOUD_PROJECT=your-project-id

# 可选: 代理设置 (如果需要)
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
export all_proxy=socks5://127.0.0.1:7890
```

### 项目 ID 配置方式

1. **环境变量** (推荐用于开发)

    ```bash
    export GOOGLE_CLOUD_PROJECT=your-project-id
    ```

2. **账户配置面板** (推荐用于多账户管理)

    - 启动应用后点击"账户配置"
    - 保存当前账户信息
    - 重启时自动使用保存的配置

3. **OAuth 认证**
    ```bash
    gemini auth login
    ```

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 开发模式运行

```bash
./start-dev.sh
```

### 3. 生产模式运行

```bash
./start.sh
```

## API 端点

服务器运行在 `http://localhost:8000`

### 支持的端点格式

-   `POST /{model}:generateContent` - 生成内容
-   `POST /{model}:streamGenerateContent` - 流式生成内容
-   `POST /{model}:countTokens` - 计算 token 数量
-   `POST /{model}:embedContent` - 内容嵌入

### 测试结果

✅ **generateContent** - 工作正常
✅ **countTokens** - 工作正常
✅ **streamGenerateContent** - 工作正常
❌ **embedContent** - 返回内部服务器错误（可能需要不同的模型名称）

### 重要发现

1. **正确的模型名称**: 使用 `gemini-2.5-flash` 和 `gemini-2.5-pro` 而不是 `gemini-1.5-flash`
2. **内容格式**: contents 数组中的每个对象必须包含 `role` 字段（"user" 或 "model"）

### 正确的请求格式

```json
{
    "contents": [
        {
            "role": "user",
            "parts": [
                {
                    "text": "Your message here"
                }
            ]
        }
    ]
}
```

## 测试

运行基本测试：

```bash
./test-api.sh
```

运行详细测试：

```bash
./test-detailed.sh
```

运行完整测试（推荐）：

```bash
./test-complete.sh
```

## 流式传输验证

经过详细测试验证，当前的流式传输实现工作正常：

-   ✅ 真正的多 chunk 流式传输
-   ✅ 正确的 SSE 格式
-   ✅ 适当的错误处理
-   ✅ 良好的性能表现

关于流式传输"有问题"的说法已被证实是误解。

## 主要变更（从 Deno 到 Node.js）

1. **导入语法**: 从 `npm:package` 改为标准的 `import`
2. **HTTP 服务器**: 从 `Deno.serve` 改为 `express`
3. **响应处理**: 从 `Response.json()` 改为 `res.json()`
4. **流式响应**: 从 `ReadableStream` 改为 Express 的 `res.write()`
5. **信号处理**: 移除了 `abortSignal` (Express 请求对象没有此属性)

## 故障排除

如果遇到 404 错误，可能需要：

1. 检查 Google Cloud 项目权限
2. 确认 API 密钥配置
3. 验证模型名称格式
4. 检查网络代理设置

## 依赖

-   `@google/gemini-cli-core`: Google Gemini CLI 核心库
-   `express`: Node.js web 框架
-   `gaxios`: HTTP 客户端
-   `typescript`: TypeScript 编译器
-   `ts-node`: TypeScript 运行时
