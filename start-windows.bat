@echo off
setlocal enabledelayedexpansion

echo Gemini CLI Chat Full Stack Application (Windows)
echo ===============================================
echo.

REM Check if in correct directory
if not exist "backend" (
    echo [ERROR] backend directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

if not exist "web" (
    echo [ERROR] web directory not found  
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

REM Check OAuth authentication
echo Checking OAuth authentication...
set AUTH_FOUND=0

set OAUTH_FILE=%USERPROFILE%\.gemini\oauth_creds.json
echo Looking for: %OAUTH_FILE%

if exist "%OAUTH_FILE%" (
    echo [OK] Found OAuth credentials file
    set AUTH_FOUND=1
) else (
    echo [INFO] OAuth credentials file not found
)

if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo [OK] Found GOOGLE_APPLICATION_CREDENTIALS: %GOOGLE_APPLICATION_CREDENTIALS%
    set AUTH_FOUND=1
)

if defined GEMINI_OAUTH_CREDS (
    echo [OK] Found GEMINI_OAUTH_CREDS: %GEMINI_OAUTH_CREDS%
    set AUTH_FOUND=1
)

if !AUTH_FOUND!==0 (
    echo.
    echo [WARNING] No OAuth authentication configuration detected
    echo.
    echo To configure authentication, you need to:
    echo 1. Get OAuth credentials from Google Cloud Console
    echo 2. Save them to: !OAUTH_FILE!
    echo.
    echo Quick setup:
    echo 1. Visit: https://console.cloud.google.com/
    echo 2. Go to APIs and Services ^> Credentials
    echo 3. Create OAuth 2.0 Client ID (Desktop application)
    echo 4. Download the JSON file
    echo 5. Copy it to: !OAUTH_FILE!
    echo 6. Set your project ID: set GOOGLE_CLOUD_PROJECT=your-project-id
    echo.
    echo [INFO] Continuing automatically with existing OAuth credentials...
    echo Authentication will be prompted during startup if needed.
) else (
    echo [OK] OAuth authentication configuration found
)

echo [OK] Environment check passed
echo.

REM Start backend
echo Starting backend service...
cd backend
start /b cmd /c "call scripts\start-dev-windows.bat"
cd ..

REM Wait for backend to start
echo Waiting for backend service to start...
timeout /t 8 /nobreak >nul

REM Check if backend started successfully
echo Checking backend service status...
set BACKEND_READY=0

for /l %%i in (1,1,10) do (
    curl -s http://localhost:8000 >nul 2>&1
    if not errorlevel 1 (
        echo [OK] Backend service started successfully (http://localhost:8000)
        set BACKEND_READY=1
        goto :backend_ready
    )
    echo Waiting for backend service... (%%i/10)
    timeout /t 2 /nobreak >nul
)

:backend_ready
if %BACKEND_READY%==0 (
    echo [ERROR] Backend service failed to start or timed out
    echo Please check backend logs or start manually: cd backend ^&^& npm start
    pause
    exit /b 1
)

REM Start frontend
echo.
echo Starting frontend service...
echo Waiting for frontend service to start...

cd web
start /b cmd /c "call npm run dev"
cd ..

REM Wait for frontend to start
timeout /t 5 /nobreak >nul

REM Check if frontend started successfully
set FRONTEND_READY=0

for /l %%i in (1,1,10) do (
    curl -s http://localhost:9000 >nul 2>&1
    if not errorlevel 1 (
        echo [OK] Frontend service started successfully (http://localhost:9000)
        set FRONTEND_READY=1
        goto :frontend_ready
    )
    curl -s http://localhost:9001 >nul 2>&1
    if not errorlevel 1 (
        echo [OK] Frontend service started successfully (http://localhost:9001)
        set FRONTEND_READY=1
        goto :frontend_ready
    )
    echo Waiting for frontend service... (%%i/10)
    timeout /t 2 /nobreak >nul
)

:frontend_ready
if %FRONTEND_READY%==0 (
    echo [WARNING] Frontend service may have failed to start, but backend is running
    echo You can still use the service via API
)

echo.
echo Full stack application started successfully!
echo ==========================================
echo Web Interface: http://localhost:9000
echo Backend API:   http://localhost:8000
echo Roo Code API:  http://localhost:8000/v1
echo.
echo Tips:
echo - If frontend port is occupied, it may automatically use port 9001
echo - Press Ctrl+C to stop services
echo - Closing this window will stop all services
echo.
echo Press any key to open browser...
pause >nul

REM Open browser
start http://localhost:9000

echo.
echo Services are running...
echo Press Ctrl+C to stop all services
echo.

REM Keep window open
:loop
timeout /t 5 /nobreak >nul
goto :loop
