@echo off
setlocal enabledelayedexpansion

echo Gemini CLI Chat Installation Test (Windows)
echo ==========================================
echo.

echo Testing system requirements...

REM Test Node.js
echo Testing Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo [FAIL] Node.js is not installed
    set TEST_FAILED=1
) else (
    for /f %%i in ('node --version') do set NODE_VERSION=%%i
    echo [PASS] Node.js %NODE_VERSION%

    REM Simple version warning for very old versions
    echo %NODE_VERSION% | findstr /r "^v1[0-7]\." >nul
    if not errorlevel 1 (
        echo [WARN] Node.js version might be too old (Recommended: 18+)
    )
)

REM Test npm
echo Testing npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo [FAIL] npm is not installed
    set TEST_FAILED=1
) else (
    for /f %%i in ('npm --version') do set NPM_VERSION=%%i
    echo [PASS] npm %NPM_VERSION%
)

REM Test curl (optional)
echo Testing curl...
curl --version >nul 2>&1
if errorlevel 1 (
    echo [WARN] curl is not available (optional for testing)
) else (
    echo [PASS] curl is available
)

echo.
echo Testing project structure...

REM Test directories
if exist "backend" (
    echo [PASS] backend directory exists
) else (
    echo [FAIL] backend directory missing
    set TEST_FAILED=1
)

if exist "web" (
    echo [PASS] web directory exists
) else (
    echo [FAIL] web directory missing
    set TEST_FAILED=1
)

if exist "package.json" (
    echo [PASS] root package.json exists
) else (
    echo [FAIL] root package.json missing
    set TEST_FAILED=1
)

if exist "backend\package.json" (
    echo [PASS] backend package.json exists
) else (
    echo [FAIL] backend package.json missing
    set TEST_FAILED=1
)

if exist "web\package.json" (
    echo [PASS] web package.json exists
) else (
    echo [FAIL] web package.json missing
    set TEST_FAILED=1
)

echo.
echo Testing authentication configuration...

REM Test config directory
if exist "%USERPROFILE%\.gemini" (
    echo [PASS] Configuration directory exists
) else (
    echo [WARN] Configuration directory does not exist
    echo        Run setup-windows.bat to create it
)

REM Test OAuth credentials
if exist "%USERPROFILE%\.gemini\oauth_creds.json" (
    echo [PASS] OAuth credentials file found
) else if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo [PASS] GOOGLE_APPLICATION_CREDENTIALS environment variable set
) else if defined GEMINI_OAUTH_CREDS (
    echo [PASS] GEMINI_OAUTH_CREDS environment variable set
) else (
    echo [WARN] No OAuth credentials found
    echo        Please configure authentication before starting
)

REM Test project ID
if defined GOOGLE_CLOUD_PROJECT (
    echo [PASS] GOOGLE_CLOUD_PROJECT environment variable set: %GOOGLE_CLOUD_PROJECT%
) else if defined GEMINI_PROJECT_ID (
    echo [PASS] GEMINI_PROJECT_ID environment variable set: %GEMINI_PROJECT_ID%
) else (
    echo [WARN] No project ID environment variable set
    echo        Please set GOOGLE_CLOUD_PROJECT=your-project-id
)

echo.
echo Testing dependencies...

REM Test root dependencies
if exist "node_modules" (
    echo [PASS] Root dependencies installed
) else (
    echo [WARN] Root dependencies not installed
    echo        Run: npm install
)

REM Test backend dependencies
if exist "backend\node_modules" (
    echo [PASS] Backend dependencies installed
) else (
    echo [WARN] Backend dependencies not installed
    echo        Run: cd backend && npm install
)

REM Test frontend dependencies
if exist "web\node_modules" (
    echo [PASS] Frontend dependencies installed
) else (
    echo [WARN] Frontend dependencies not installed
    echo        Run: cd web && npm install
)

echo.
echo Testing build...

REM Test backend build
if exist "backend\dist" (
    echo [PASS] Backend build directory exists
) else (
    echo [WARN] Backend not built
    echo        Run: cd backend && npm run build
)

REM Test frontend build
if exist "web\dist" (
    echo [PASS] Frontend build directory exists
) else (
    echo [INFO] Frontend build directory not found (normal for dev mode)
)

echo.
echo ==========================================

if defined TEST_FAILED (
    echo [RESULT] Some tests FAILED
    echo Please fix the issues above before starting the application
) else (
    echo [RESULT] All critical tests PASSED
    echo You can now start the application with: start-windows.bat
)

echo.
echo Test completed!
pause
