@echo off
setlocal enabledelayedexpansion

echo Gemini CLI Chat Auto Setup Script (Windows)
echo ==========================================
echo.

REM Check system requirements
echo Checking system requirements...

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed
    echo Please install Node.js 18+ from: https://nodejs.org/
    pause
    exit /b 1
)

for /f %%i in ('node --version') do set NODE_VERSION=%%i
echo [OK] Node.js %NODE_VERSION%

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed
    pause
    exit /b 1
)

for /f %%i in ('npm --version') do set NPM_VERSION=%%i
echo [OK] npm %NPM_VERSION%
echo.

REM Install dependencies
echo Installing project dependencies...
echo This may take a few minutes...
echo.

echo [1/3] Installing root dependencies...
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install root dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)
echo [OK] Root dependencies installed

echo [2/3] Installing backend dependencies...
cd backend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install backend dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)
cd ..
echo [OK] Backend dependencies installed

echo [3/3] Installing frontend dependencies...
cd web
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    echo Please check your internet connection and try again
    pause
    exit /b 1
)
cd ..
echo [OK] Frontend dependencies installed

echo.
echo [OK] All dependencies installed successfully!
echo.

REM Create config directory
echo Creating configuration directory...
if not exist "%USERPROFILE%\.gemini" mkdir "%USERPROFILE%\.gemini"
echo [OK] Configuration directory: %USERPROFILE%\.gemini
echo.

REM Check authentication configuration
echo Checking authentication configuration...

set AUTH_CONFIGURED=0
if exist "%USERPROFILE%\.gemini\oauth_creds.json" (
    echo [OK] Found OAuth credentials file
    set AUTH_CONFIGURED=1
)

if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo [OK] Found GOOGLE_APPLICATION_CREDENTIALS environment variable
    set AUTH_CONFIGURED=1
)

if defined GEMINI_OAUTH_CREDS (
    echo [OK] Found GEMINI_OAUTH_CREDS environment variable
    set AUTH_CONFIGURED=1
)

if %AUTH_CONFIGURED%==0 (
    echo [WARNING] No OAuth authentication found
    echo.
    echo You need to set up authentication before using the application.
    echo.
    echo Quick setup guide:
    echo 1. Visit: https://console.cloud.google.com/
    echo 2. Create a project and enable 'Generative Language API'
    echo 3. Go to 'APIs and Services' ^> 'Credentials'
    echo 4. Create 'OAuth 2.0 Client ID' (Desktop application)
    echo 5. Download the JSON file
    echo 6. Copy it to: %USERPROFILE%\.gemini\oauth_creds.json
    echo 7. Set project ID: set GOOGLE_CLOUD_PROJECT=your-project-id
    echo.
    echo For detailed instructions, run: setup-auth.bat
)

REM Check project ID
if defined GOOGLE_CLOUD_PROJECT (
    echo [OK] Project ID: %GOOGLE_CLOUD_PROJECT%
) else if defined GEMINI_PROJECT_ID (
    echo [OK] Project ID: %GEMINI_PROJECT_ID%
) else (
    echo [WARNING] Project ID not set
    echo Please set: set GOOGLE_CLOUD_PROJECT=your-project-id
)

echo.
echo ==========================================
echo Installation completed successfully!
echo ==========================================
echo.

if %AUTH_CONFIGURED%==1 (
    echo [READY] You can now start the application!
    echo.
    echo To start: start-windows.bat
    echo To test:  test-windows.bat
) else (
    echo [NEXT STEP] Set up authentication first:
    echo.
    echo Option 1: Run setup-auth.bat (guided setup)
    echo Option 2: Manual setup (see warnings above)
    echo.
    echo After authentication setup, run: start-windows.bat
)

echo.
echo Documentation: INSTALLATION.md
echo Troubleshooting: debug-auth.bat
echo.
echo Press any key to continue...
pause >nul
