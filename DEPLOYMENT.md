# 🚀 Gemini CLI Chat 部署指南

## 📋 部署方式概览

### 1. 本地开发部署 (已完成)
- 适用于个人开发和测试
- 使用 `./start.sh` 或 `start.bat`

### 2. 服务器部署 (生产环境)
- 适用于团队共享或远程访问
- 需要配置域名、SSL、反向代理等

### 3. 容器化部署 (Docker)
- 适用于云平台和容器环境
- 便于扩展和管理

## 🖥️ 服务器手动部署

### 准备工作

#### 系统要求
- **操作系统**: Ubuntu 20.04+, CentOS 8+, 或其他 Linux 发行版
- **Node.js**: 18.0+
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间
- **网络**: 需要访问 Google AI API

#### 安装基础环境

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y curl wget git nginx

# 安装 Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
sudo yum update -y
sudo yum install -y curl wget git nginx

# 安装 Node.js 18+
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

### 步骤 1: 部署应用

```bash
# 1. 创建应用目录
sudo mkdir -p /opt/gemini-cli-chat
sudo chown $USER:$USER /opt/gemini-cli-chat
cd /opt/gemini-cli-chat

# 2. 克隆或上传项目
git clone https://github.com/your-username/gemini-cli-chat.git .
# 或者上传压缩包并解压

# 3. 安装依赖
npm install
cd backend && npm install && cd ..
cd web && npm install && cd ..

# 4. 构建项目
cd backend && npm run build && cd ..
cd web && npm run build && cd ..
```

### 步骤 2: 配置认证

```bash
# 1. 创建系统级配置目录
sudo mkdir -p /etc/gemini-cli-chat
sudo chown $USER:$USER /etc/gemini-cli-chat

# 2. 放置 OAuth 凭证文件
cp /path/to/your/oauth_creds.json /etc/gemini-cli-chat/oauth_creds.json

# 3. 设置环境变量
sudo tee /etc/environment << EOF
GOOGLE_CLOUD_PROJECT=your-project-id
GEMINI_OAUTH_CREDS=/etc/gemini-cli-chat/oauth_creds.json
NODE_ENV=production
EOF

# 4. 重新加载环境变量
source /etc/environment
```

### 步骤 3: 创建系统服务

```bash
# 创建后端服务
sudo tee /etc/systemd/system/gemini-backend.service << EOF
[Unit]
Description=Gemini CLI Chat Backend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/gemini-cli-chat/backend
Environment=NODE_ENV=production
Environment=GOOGLE_CLOUD_PROJECT=your-project-id
Environment=GEMINI_OAUTH_CREDS=/etc/gemini-cli-chat/oauth_creds.json
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 创建前端服务 (使用 serve)
sudo npm install -g serve

sudo tee /etc/systemd/system/gemini-frontend.service << EOF
[Unit]
Description=Gemini CLI Chat Frontend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/gemini-cli-chat/web
ExecStart=/usr/bin/serve -s dist -l 3000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable gemini-backend gemini-frontend
sudo systemctl start gemini-backend gemini-frontend

# 检查服务状态
sudo systemctl status gemini-backend
sudo systemctl status gemini-frontend
```

### 步骤 4: 配置 Nginx 反向代理

```bash
# 创建 Nginx 配置
sudo tee /etc/nginx/sites-available/gemini-cli-chat << EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名

    # 前端静态文件
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # 后端 API
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # 支持 SSE (Server-Sent Events)
        proxy_buffering off;
        proxy_cache off;
    }

    # OpenAI 兼容 API
    location /v1/ {
        proxy_pass http://localhost:8000/v1/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # 支持流式响应
        proxy_buffering off;
        proxy_cache off;
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/gemini-cli-chat /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 步骤 5: 配置 SSL (可选但推荐)

```bash
# 安装 Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取 SSL 证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🐳 Docker 容器化部署

### 创建 Dockerfile

```bash
# 创建后端 Dockerfile
cat > backend/Dockerfile << EOF
FROM node:18-alpine

WORKDIR /app

# 复制 package 文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

EXPOSE 8000

CMD ["npm", "start"]
EOF

# 创建前端 Dockerfile
cat > web/Dockerfile << EOF
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
EOF
```

### 创建 Docker Compose

```bash
cat > docker-compose.yml << EOF
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    environment:
      - NODE_ENV=production
      - GOOGLE_CLOUD_PROJECT=\${GOOGLE_CLOUD_PROJECT}
      - GEMINI_OAUTH_CREDS=/app/oauth_creds.json
    volumes:
      - ./oauth_creds.json:/app/oauth_creds.json:ro
    restart: unless-stopped

  frontend:
    build: ./web
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
EOF

# 部署
docker-compose up -d
```

## 🔧 生产环境优化

### 性能优化

```bash
# 1. 启用 PM2 进程管理
npm install -g pm2

# 创建 PM2 配置
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'gemini-backend',
    script: './backend/dist/code-assist-api.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      GOOGLE_CLOUD_PROJECT: 'your-project-id',
      GEMINI_OAUTH_CREDS: '/etc/gemini-cli-chat/oauth_creds.json'
    }
  }]
};
EOF

# 启动应用
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 监控和日志

```bash
# 1. 配置日志轮转
sudo tee /etc/logrotate.d/gemini-cli-chat << EOF
/var/log/gemini-cli-chat/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        systemctl reload gemini-backend
    endscript
}
EOF

# 2. 设置监控
# 可以使用 Prometheus + Grafana 或其他监控工具
```

## 🔒 安全配置

### 防火墙设置

```bash
# Ubuntu UFW
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 访问控制

```bash
# 在 Nginx 中添加 IP 白名单
location /api/ {
    allow ***********/24;  # 允许内网访问
    allow your-office-ip;   # 允许办公室 IP
    deny all;              # 拒绝其他所有访问
    
    proxy_pass http://localhost:8000/;
    # ... 其他配置
}
```

## 📊 部署验证

### 检查服务状态

```bash
# 检查端口
sudo netstat -tlnp | grep -E ':(80|443|8000|3000)'

# 检查服务
sudo systemctl status gemini-backend gemini-frontend nginx

# 检查日志
sudo journalctl -u gemini-backend -f
sudo journalctl -u gemini-frontend -f

# 测试 API
curl http://your-domain.com/v1/models
curl -X POST http://your-domain.com/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "gemini-2.5-flash", "messages": [{"role": "user", "content": "Hello!"}]}'
```

## 🔄 更新和维护

### 应用更新

```bash
# 1. 备份当前版本
sudo cp -r /opt/gemini-cli-chat /opt/gemini-cli-chat.backup.$(date +%Y%m%d)

# 2. 拉取新代码
cd /opt/gemini-cli-chat
git pull origin main

# 3. 更新依赖和构建
npm install
cd backend && npm install && npm run build && cd ..
cd web && npm install && npm run build && cd ..

# 4. 重启服务
sudo systemctl restart gemini-backend gemini-frontend
```

### 数据备份

```bash
# 备份配置文件
sudo tar -czf /backup/gemini-config-$(date +%Y%m%d).tar.gz \
  /etc/gemini-cli-chat \
  /etc/systemd/system/gemini-*.service \
  /etc/nginx/sites-available/gemini-cli-chat
```

这样就完成了完整的手动部署配置！🎉
