@echo off
setlocal enabledelayedexpansion

echo Gemini CLI Chat Auto Setup Script (Windows)
echo ==========================================
echo.

REM Check system requirements
echo Checking system requirements...

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed
    echo Please install Node.js 18+ : https://nodejs.org/
    pause
    exit /b 1
)

REM Get Node.js version and display it
for /f %%i in ('node --version') do set NODE_VERSION=%%i
echo [OK] Node.js %NODE_VERSION%

REM Version check removed - Node.js 18+ is recommended but not strictly enforced

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed
    pause
    exit /b 1
)

for /f %%i in ('npm --version') do set NPM_VERSION=%%i
echo [OK] npm %NPM_VERSION%
echo.

REM Install dependencies
echo Installing project dependencies...

echo Installing root dependencies...
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install root dependencies
    pause
    exit /b 1
)

echo Installing backend dependencies...
cd backend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo Installing frontend dependencies...
cd web
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo [OK] Dependencies installed successfully
echo.

REM Create config directory
echo Creating configuration directory...
if not exist "%USERPROFILE%\.gemini" mkdir "%USERPROFILE%\.gemini"
echo [OK] Configuration directory created: %USERPROFILE%\.gemini
echo.

REM Check authentication configuration
echo Checking authentication configuration...

if exist "%USERPROFILE%\.gemini\oauth_creds.json" (
    echo [OK] Found existing OAuth credentials file
) else if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo [OK] Found GOOGLE_APPLICATION_CREDENTIALS environment variable
) else if defined GEMINI_OAUTH_CREDS (
    echo [OK] Found GEMINI_OAUTH_CREDS environment variable
) else (
    echo [WARNING] No OAuth authentication configuration found
    echo.
    echo Please complete the following configuration steps:
    echo.
    echo 1. Get Google Cloud Project ID:
    echo    - Visit https://console.cloud.google.com/
    echo    - Create or select a project
    echo    - Note down the project ID
    echo.
    echo 2. Enable Gemini API:
    echo    - Search for 'Generative Language API' in Google Cloud Console
    echo    - Click Enable
    echo.
    echo 3. Create OAuth 2.0 credentials:
    echo    - Go to 'APIs and Services' then 'Credentials'
    echo    - Create 'OAuth 2.0 Client IDs' (Desktop application)
    echo    - Download the JSON file
    echo.
    echo 4. Configure authentication:
    echo    copy "path\to\downloaded\oauth_creds.json" "%USERPROFILE%\.gemini\oauth_creds.json"
    echo    set GOOGLE_CLOUD_PROJECT=your-project-id
    echo.
    echo 5. (Optional) If in China, set proxy:
    echo    set https_proxy=http://127.0.0.1:7890
    echo    set http_proxy=http://127.0.0.1:7890
    echo.
)

REM Check project ID
if defined GOOGLE_CLOUD_PROJECT (
    echo [OK] Project ID: %GOOGLE_CLOUD_PROJECT%
) else if defined GEMINI_PROJECT_ID (
    echo [OK] Project ID: %GEMINI_PROJECT_ID%
) else (
    echo [WARNING] Project ID not set
    echo Please set: set GOOGLE_CLOUD_PROJECT=your-project-id
)

echo.
echo Installation completed!
echo.
echo Next steps:
echo 1. Make sure authentication is configured (see warnings above)
echo 2. Run the application: start-windows.bat
echo 3. Visit: http://localhost:9000
echo.
echo Documentation: Check INSTALLATION.md
echo Troubleshooting: See INSTALLATION.md troubleshooting section
echo.
echo Ready to go! Run 'start-windows.bat' to start the application
echo.
pause
