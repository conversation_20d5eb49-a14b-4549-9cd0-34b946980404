@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 Gemini CLI Chat 稳定启动 (编译模式 - Windows)
echo ================================================
echo.

REM 检查OAuth认证
set AUTH_FOUND=0

if exist "%USERPROFILE%\.gemini\oauth_creds.json" set AUTH_FOUND=1
if defined GOOGLE_APPLICATION_CREDENTIALS set AUTH_FOUND=1
if defined GEMINI_OAUTH_CREDS set AUTH_FOUND=1

if %AUTH_FOUND%==0 (
    echo ⚠️  警告: 未检测到OAuth认证配置
    echo 请执行以下操作之一:
    echo 1. 运行 'gemini auth login' 设置默认账户 ^(推荐^)
    echo 2. 设置 'set GEMINI_OAUTH_CREDS=C:\path\to\oauth_creds.json' ^(多账户^)
    echo 3. 使用多账户管理工具: cd backend ^&^& scripts\manage-accounts.bat
    pause
    exit /b 1
) else (
    echo ✅ OAuth认证配置检查通过
)

echo ✅ 环境检查通过
echo.

REM 启动后端 (编译模式)
echo 🔧 启动后端服务 (编译模式)...
cd backend
start /b cmd /c "call scripts\start-compiled.bat"
set BACKEND_PID=%ERRORLEVEL%
cd ..

REM 等待后端启动
echo ⏳ 等待后端服务启动...
timeout /t 10 /nobreak >nul

REM 检查后端是否启动成功
echo 🔍 检查后端服务状态...
set BACKEND_READY=0

for /l %%i in (1,1,15) do (
    curl -s http://localhost:8000 >nul 2>&1
    if not errorlevel 1 (
        echo ✅ 后端服务启动成功 ^(http://localhost:8000^)
        set BACKEND_READY=1
        goto :backend_ready
    ) else (
        echo ⏳ 等待后端服务... ^(%%i/15^)
        timeout /t 2 /nobreak >nul
    )
)

:backend_ready
if %BACKEND_READY%==0 (
    echo ❌ 后端服务启动失败或超时
    echo 请检查后端日志
    pause
    exit /b 1
)

REM 启动前端 (生产模式)
echo.
echo 🎨 启动前端服务 (生产模式)...
echo ⏳ 等待前端服务启动...

cd web
start /b cmd /c "call npm run build && npx serve -s dist -l 9000"
cd ..

REM 等待前端启动
timeout /t 8 /nobreak >nul

REM 检查前端是否启动成功
set FRONTEND_READY=0

for /l %%i in (1,1,10) do (
    curl -s http://localhost:9000 >nul 2>&1
    if not errorlevel 1 (
        echo ✅ 前端服务启动成功 ^(http://localhost:9000^)
        set FRONTEND_READY=1
        goto :frontend_ready
    ) else (
        echo ⏳ 等待前端服务... ^(%%i/10^)
        timeout /t 2 /nobreak >nul
    )
)

:frontend_ready
if %FRONTEND_READY%==0 (
    echo ⚠️  前端服务可能启动失败，但后端服务正常
    echo 你仍然可以通过 API 使用服务
)

echo.
echo 🎉 稳定版应用启动成功！
echo ================================
echo 📱 前端界面: http://localhost:9000
echo 🔌 后端API:  http://localhost:8000
echo 🛠️  Roo Code API: http://localhost:8000/v1
echo.
echo 💡 提示:
echo - 这是编译后的稳定版本，性能更好
echo - 按 Ctrl+C 可以停止服务
echo - 关闭此窗口将停止所有服务
echo.
echo 按任意键打开浏览器...
pause >nul

REM 打开浏览器
start http://localhost:9000

echo.
echo 🔄 服务正在运行中...
echo 按 Ctrl+C 停止所有服务
echo.

REM 保持窗口打开
:loop
timeout /t 5 /nobreak >nul
goto :loop
