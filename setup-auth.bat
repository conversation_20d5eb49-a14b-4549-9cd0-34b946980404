@echo off
setlocal enabledelayedexpansion

echo Gemini CLI Chat Authentication Setup Helper
echo ===========================================
echo.

echo This script will help you set up authentication for Gemini CLI Chat.
echo.

REM Check if .gemini directory exists
set GEMINI_DIR=%USERPROFILE%\.gemini
if not exist "%GEMINI_DIR%" (
    echo Creating configuration directory...
    mkdir "%GEMINI_DIR%"
    if exist "%GEMINI_DIR%" (
        echo [OK] Created: %GEMINI_DIR%
    ) else (
        echo [ERROR] Failed to create directory: %GEMINI_DIR%
        pause
        exit /b 1
    )
) else (
    echo [OK] Configuration directory exists: %GEMINI_DIR%
)
echo.

REM Check current authentication status
set OAUTH_FILE=%GEMINI_DIR%\oauth_creds.json
echo Checking current authentication status...

if exist "%OAUTH_FILE%" (
    echo [OK] OAuth credentials file already exists: %OAUTH_FILE%
    echo.
    choice /c YN /m "Do you want to replace the existing credentials"
    if errorlevel 2 (
        echo Keeping existing credentials.
        goto :check_project_id
    )
    echo.
)

echo.
echo ========================================
echo Step 1: Get OAuth Credentials
echo ========================================
echo.
echo You need to get OAuth 2.0 credentials from Google Cloud Console:
echo.
echo 1. Open your web browser and go to:
echo    https://console.cloud.google.com/
echo.
echo 2. Create a new project or select an existing one
echo.
echo 3. Enable the Generative Language API:
echo    - Go to "APIs and Services" ^> "Library"
echo    - Search for "Generative Language API"
echo    - Click "Enable"
echo.
echo 4. Create OAuth 2.0 credentials:
echo    - Go to "APIs and Services" ^> "Credentials"
echo    - Click "Create Credentials" ^> "OAuth 2.0 Client IDs"
echo    - Choose "Desktop application"
echo    - Give it a name (e.g., "Gemini CLI Chat")
echo    - Click "Create"
echo.
echo 5. Download the credentials:
echo    - Click the download button next to your new OAuth client
echo    - Save the JSON file to your computer
echo.
echo Press any key when you have downloaded the OAuth credentials file...
pause >nul

echo.
echo ========================================
echo Step 2: Copy Credentials File
echo ========================================
echo.
echo Now you need to copy the downloaded JSON file to:
echo %OAUTH_FILE%
echo.
echo Please drag and drop the downloaded JSON file into this window,
echo or type the full path to the file:
echo.
set /p SOURCE_FILE="Path to downloaded OAuth JSON file: "

REM Remove quotes if present
set SOURCE_FILE=%SOURCE_FILE:"=%

if not exist "%SOURCE_FILE%" (
    echo [ERROR] File not found: %SOURCE_FILE%
    echo Please check the path and try again.
    pause
    exit /b 1
)

echo.
echo Copying credentials file...
copy "%SOURCE_FILE%" "%OAUTH_FILE%"
if errorlevel 1 (
    echo [ERROR] Failed to copy credentials file
    pause
    exit /b 1
)

echo [OK] Credentials file copied successfully!

:check_project_id
echo.
echo ========================================
echo Step 3: Set Project ID
echo ========================================
echo.

if defined GOOGLE_CLOUD_PROJECT (
    echo [OK] Project ID already set: %GOOGLE_CLOUD_PROJECT%
    echo.
    choice /c YN /m "Do you want to change the project ID"
    if errorlevel 2 (
        goto :completion
    )
    echo.
)

echo Please enter your Google Cloud Project ID.
echo You can find this in the Google Cloud Console at the top of the page.
echo.
set /p PROJECT_ID="Google Cloud Project ID: "

if "%PROJECT_ID%"=="" (
    echo [WARNING] No project ID entered. You can set it later with:
    echo set GOOGLE_CLOUD_PROJECT=your-project-id
) else (
    echo.
    echo Setting project ID for this session...
    set GOOGLE_CLOUD_PROJECT=%PROJECT_ID%
    echo [OK] Project ID set to: %PROJECT_ID%
    echo.
    echo To make this permanent, add this to your system environment variables:
    echo GOOGLE_CLOUD_PROJECT=%PROJECT_ID%
)

:completion
echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo Authentication has been configured successfully!
echo.
echo Configuration summary:
echo - OAuth credentials: %OAUTH_FILE%
if defined GOOGLE_CLOUD_PROJECT (
    echo - Project ID: %GOOGLE_CLOUD_PROJECT%
) else (
    echo - Project ID: [Not set - will be prompted during startup]
)
echo.
echo You can now start the application with:
echo start-windows.bat
echo.
echo Press any key to exit...
pause >nul
