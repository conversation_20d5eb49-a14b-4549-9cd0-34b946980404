# 🚀 Gemini CLI Chat

一个功能强大的本地化 Google Gemini AI 聊天应用，支持 Web 界面、API 接口和 Roo Code 集成。

## ✨ 主要特性

- 🤖 **多模型支持**: gemini-2.5-flash, gemini-2.5-pro, gemini-1.5-flash, gemini-1.5-pro
- 💬 **流式对话**: 实时打字效果，类似 ChatGPT 体验
- 🌐 **Web 界面**: 现代化响应式设计，支持多账户管理
- 🔌 **API 接口**: 完整的 RESTful API 和 OpenAI 兼容接口
- 🛠️ **Roo Code 集成**: 支持作为 AI 代码助手使用
- 🔐 **安全认证**: OAuth 2.0 个人账户认证
- 🌍 **代理支持**: 支持网络代理配置
- 📊 **实用工具**: Token 计数、内容嵌入等功能

## 🚀 快速开始

### 一键安装

```bash
# 1. 克隆项目
git clone https://github.com/your-username/gemini-cli-chat.git
cd gemini-cli-chat

# 2. 运行自动安装脚本
./setup.sh

# 3. 配置认证 (按照脚本提示)
# 4. 启动应用
./start.sh
```

### 手动安装

详细安装步骤请查看 [INSTALLATION.md](INSTALLATION.md)

## 🎯 使用方式

### Web 界面
启动后访问 http://localhost:9000

### API 接口
- **Gemini 原生 API**: `http://localhost:8000/{model}:generateContent`
- **OpenAI 兼容 API**: `http://localhost:8000/v1/chat/completions`

### Roo Code 集成
在 Roo Code 中配置：
```
API Provider: OpenAI Compatible
Base URL: http://localhost:8000/v1
API Key: any-key-works
Model: gemini-2.5-flash
```

## 📋 系统要求

- **Node.js**: 18.0+ 
- **npm**: 8.0+
- **操作系统**: macOS, Linux, Windows (WSL)
- **网络**: 需要访问 Google AI API

## 🔐 认证配置

### 获取 Google Cloud 项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 "Generative Language API"
4. 创建 OAuth 2.0 凭证 (Desktop application)
5. 下载 JSON 文件到 `~/.gemini/oauth_creds.json`

### 设置环境变量
```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"

# 如果在中国大陆，可能需要代理
export https_proxy=http://127.0.0.1:7890
export http_proxy=http://127.0.0.1:7890
```

## 📚 API 文档

### Gemini 原生 API

```bash
# 生成内容
curl -X POST http://localhost:8000/gemini-2.5-flash:generateContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user",
        "parts": [{"text": "Hello!"}]
      }
    ]
  }'

# 流式生成
curl -X POST http://localhost:8000/gemini-2.5-flash:streamGenerateContent \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [
      {
        "role": "user", 
        "parts": [{"text": "Count from 1 to 5"}]
      }
    ]
  }'
```

### OpenAI 兼容 API

```bash
# 聊天完成
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'

# 流式聊天
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.5-flash", 
    "messages": [{"role": "user", "content": "Count from 1 to 5"}],
    "stream": true
  }'

# 模型列表
curl http://localhost:8000/v1/models
```

## 🛠️ 开发

### 项目结构
```
gemini-cli-chat/
├── backend/          # 后端 API 服务
│   ├── src/         # TypeScript 源代码
│   ├── dist/        # 编译后的 JavaScript
│   └── scripts/     # 启动脚本
├── web/             # 前端界面
│   ├── index.html   # 主页面
│   ├── main.js      # 主要逻辑
│   └── style.css    # 样式文件
├── docs/            # 项目文档
├── INSTALLATION.md  # 详细安装指南
└── setup.sh         # 自动安装脚本
```

### 开发模式启动
```bash
# 后端开发模式
cd backend && npm run dev

# 前端开发模式  
cd web && npm run dev
```

## 🐛 故障排除

### 常见问题

1. **认证失败**: 检查 OAuth 凭证文件和项目 ID
2. **端口占用**: 使用 `lsof -i :8000` 检查端口占用
3. **网络问题**: 配置代理或检查网络连接
4. **依赖问题**: 清理 `node_modules` 重新安装

详细故障排除请查看 [INSTALLATION.md](INSTALLATION.md)

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

- 📖 文档: [INSTALLATION.md](INSTALLATION.md)
- 🐛 问题: [GitHub Issues](https://github.com/your-username/gemini-cli-chat/issues)
- 💬 讨论: [GitHub Discussions](https://github.com/your-username/gemini-cli-chat/discussions)
