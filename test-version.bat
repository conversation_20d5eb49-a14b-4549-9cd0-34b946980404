@echo off
setlocal enabledelayedexpansion

echo Testing Node.js version detection...
echo.

REM Get Node.js version
for /f %%i in ('node --version') do set NODE_VERSION=%%i
echo Raw version: %NODE_VERSION%

REM Remove 'v' prefix
set NODE_VERSION_CLEAN=%NODE_VERSION:v=%
echo Clean version: %NODE_VERSION_CLEAN%

REM Extract major version
for /f "tokens=1 delims=." %%i in ("%NODE_VERSION_CLEAN%") do set NODE_MAJOR=%%i
echo Major version: %NODE_MAJOR%

REM Test numeric comparison
set /a NODE_MAJOR_NUM=%NODE_MAJOR% 2>nul
echo Major version as number: %NODE_MAJOR_NUM%

if %NODE_MAJOR_NUM% GEQ 18 (
    echo [OK] Version is 18 or higher
) else (
    echo [ERROR] Version is lower than 18
)

echo.
echo Test completed!
pause
