#!/bin/bash

echo "🔄 Gemini CLI Chat 应用更新脚本"
echo "==============================="
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ] || [ ! -d "backend" ] || [ ! -d "web" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 检查是否为生产环境
if [ ! -f "/etc/systemd/system/gemini-backend.service" ]; then
    echo "❌ 未检测到生产环境部署，请先运行 ./deploy.sh"
    exit 1
fi

echo "📋 当前状态检查..."

# 检查服务状态
if systemctl is-active --quiet gemini-backend; then
    echo "✅ 后端服务运行中"
else
    echo "⚠️  后端服务未运行"
fi

if systemctl is-active --quiet gemini-frontend; then
    echo "✅ 前端服务运行中"
else
    echo "⚠️  前端服务未运行"
fi

echo ""
read -p "确认开始更新? (y/N): " CONFIRM
if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
    echo "❌ 更新已取消"
    exit 1
fi

echo ""
echo "🔄 开始更新..."

# 1. 备份当前版本
echo "💾 备份当前版本..."
BACKUP_DIR="/opt/gemini-cli-chat.backup.$(date +%Y%m%d_%H%M%S)"
sudo cp -r /opt/gemini-cli-chat "$BACKUP_DIR"
echo "✅ 备份完成: $BACKUP_DIR"

# 2. 停止服务
echo "⏹️  停止服务..."
sudo systemctl stop gemini-backend gemini-frontend

# 3. 更新代码
echo "📥 更新代码..."
cd /opt/gemini-cli-chat

if [ -d ".git" ]; then
    # Git 仓库更新
    git fetch origin
    git pull origin main
    if [ $? -ne 0 ]; then
        echo "❌ Git 更新失败"
        echo "🔄 恢复备份..."
        sudo systemctl start gemini-backend gemini-frontend
        exit 1
    fi
else
    echo "⚠️  非 Git 仓库，请手动更新代码"
fi

# 4. 更新依赖
echo "📦 更新依赖..."

# 根目录依赖
npm install
if [ $? -ne 0 ]; then
    echo "❌ 根目录依赖更新失败"
    exit 1
fi

# 后端依赖
cd backend
npm install
if [ $? -ne 0 ]; then
    echo "❌ 后端依赖更新失败"
    exit 1
fi
cd ..

# 前端依赖
cd web
npm install
if [ $? -ne 0 ]; then
    echo "❌ 前端依赖更新失败"
    exit 1
fi
cd ..

echo "✅ 依赖更新完成"

# 5. 构建项目
echo "🔨 构建项目..."

# 构建后端
cd backend
npm run build
if [ $? -ne 0 ]; then
    echo "❌ 后端构建失败"
    exit 1
fi
cd ..

# 构建前端
cd web
npm run build
if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..

echo "✅ 构建完成"

# 6. 重启服务
echo "🚀 重启服务..."
sudo systemctl start gemini-backend gemini-frontend

# 等待服务启动
sleep 5

# 7. 验证更新
echo "🔍 验证更新..."

# 检查服务状态
if systemctl is-active --quiet gemini-backend; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    echo "📋 查看日志: sudo journalctl -u gemini-backend -n 20"
    exit 1
fi

if systemctl is-active --quiet gemini-frontend; then
    echo "✅ 前端服务启动成功"
else
    echo "❌ 前端服务启动失败"
    echo "📋 查看日志: sudo journalctl -u gemini-frontend -n 20"
    exit 1
fi

# 测试 API
echo "🧪 测试 API..."
sleep 3

if curl -s http://localhost:8000/v1/models > /dev/null; then
    echo "✅ API 响应正常"
else
    echo "❌ API 无响应"
    echo "🔄 可能需要回滚，备份位置: $BACKUP_DIR"
    exit 1
fi

# 8. 清理旧备份 (保留最近5个)
echo "🧹 清理旧备份..."
BACKUP_COUNT=$(ls -1d /opt/gemini-cli-chat.backup.* 2>/dev/null | wc -l)
if [ "$BACKUP_COUNT" -gt 5 ]; then
    ls -1td /opt/gemini-cli-chat.backup.* | tail -n +6 | xargs sudo rm -rf
    echo "✅ 清理完成，保留最近5个备份"
fi

echo ""
echo "🎉 更新完成！"
echo "=========================="
echo "📱 Web 界面: http://$(hostname -I | awk '{print $1}')"
echo "🔌 API 端点: http://$(hostname -I | awk '{print $1}')/v1"
echo ""
echo "📋 更新信息:"
echo "- 备份位置: $BACKUP_DIR"
echo "- 更新时间: $(date)"
echo ""
echo "🔧 如果遇到问题:"
echo "1. 查看服务日志: sudo journalctl -u gemini-backend -f"
echo "2. 重启服务: sudo systemctl restart gemini-backend gemini-frontend"
echo "3. 回滚版本: sudo systemctl stop gemini-backend gemini-frontend && sudo rm -rf /opt/gemini-cli-chat && sudo mv $BACKUP_DIR /opt/gemini-cli-chat && sudo systemctl start gemini-backend gemini-frontend"
echo ""
echo "✅ 更新成功完成！"
