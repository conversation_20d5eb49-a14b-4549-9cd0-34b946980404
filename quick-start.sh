#!/bin/bash

echo "🚀 Gemini CLI Chat 快速启动"
echo "========================="
echo ""

# 检查OAuth认证
if [ ! -f "$HOME/.gemini/oauth_creds.json" ] && [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ] && [ -z "$GEMINI_OAUTH_CREDS" ]; then
    echo "⚠️  未检测到OAuth认证，将在后端启动时进行认证"
else
    echo "✅ OAuth认证配置检查通过"
fi

echo ""

# 启动后端
echo "🔧 启动后端服务..."
cd backend
npm run dev &
BACKEND_PID=$!
echo "后端进程ID: $BACKEND_PID"
cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动 (15秒)..."
sleep 15

# 启动前端
echo ""
echo "🎨 启动前端服务..."
cd web
npm run dev &
FRONTEND_PID=$!
echo "前端进程ID: $FRONTEND_PID"
cd ..

echo ""
echo "🎉 服务启动完成！"
echo "=================================="
echo "📱 前端界面: http://localhost:9000"
echo "🔌 后端API:  http://localhost:8000"
echo ""
echo "进程信息:"
echo "  后端PID: $BACKEND_PID"
echo "  前端PID: $FRONTEND_PID"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; echo "✅ 所有服务已停止"; exit 0' INT

# 保持脚本运行
wait
