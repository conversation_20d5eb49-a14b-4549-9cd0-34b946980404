@echo off
setlocal enabledelayedexpansion

echo Testing batch file syntax...
echo.

echo Testing backend check loop...
set BACKEND_READY=0

for /l %%i in (1,1,3) do (
    echo Testing iteration %%i...
    REM Simulate curl check (always fail for test)
    if 1==0 (
        echo Backend found
        set BACKEND_READY=1
        goto :backend_ready
    )
    echo Backend not ready, waiting...
)

:backend_ready
if %BACKEND_READY%==1 (
    echo Backend is ready
) else (
    echo Backend not ready after all attempts
)

echo.
echo Testing frontend check loop...
set FRONTEND_READY=0

for /l %%i in (1,1,3) do (
    echo Testing iteration %%i...
    REM Simulate curl checks (always fail for test)
    if 1==0 (
        echo Frontend found on port 9000
        set FRONTEND_READY=1
        goto :frontend_ready
    )
    if 1==0 (
        echo Frontend found on port 9001
        set FRONTEND_READY=1
        goto :frontend_ready
    )
    echo Frontend not ready, waiting...
)

:frontend_ready
if %FRONTEND_READY%==1 (
    echo Frontend is ready
) else (
    echo Frontend not ready after all attempts
)

echo.
echo Syntax test completed successfully!
echo No "此时不应有 else" errors should appear.
pause
