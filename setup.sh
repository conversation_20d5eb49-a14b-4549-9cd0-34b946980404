#!/bin/bash

echo "🚀 Gemini CLI Chat 自动安装脚本"
echo "================================"
echo ""

# 检查系统要求
echo "🔍 检查系统要求..."

# 检查 Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装"
    echo "请先安装 Node.js 18+ : https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本过低 (当前: $(node -v), 需要: 18+)"
    echo "请升级 Node.js: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js $(node -v)"

# 检查 npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装"
    exit 1
fi

echo "✅ npm $(npm -v)"
echo ""

# 安装依赖
echo "📦 安装项目依赖..."

echo "🔧 安装根目录依赖..."
npm install

echo "🔧 安装后端依赖..."
cd backend
npm install
cd ..

echo "🔧 安装前端依赖..."
cd web
npm install
cd ..

echo "✅ 依赖安装完成"
echo ""

# 创建配置目录
echo "📁 创建配置目录..."
mkdir -p ~/.gemini
echo "✅ 配置目录创建完成: ~/.gemini"
echo ""

# 检查认证配置
echo "🔐 检查认证配置..."

if [ -f "$HOME/.gemini/oauth_creds.json" ]; then
    echo "✅ 找到现有的 OAuth 凭证文件"
elif [ -n "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
    echo "✅ 找到 GOOGLE_APPLICATION_CREDENTIALS 环境变量"
elif [ -n "$GEMINI_OAUTH_CREDS" ]; then
    echo "✅ 找到 GEMINI_OAUTH_CREDS 环境变量"
else
    echo "⚠️  未找到 OAuth 认证配置"
    echo ""
    echo "📋 请完成以下配置步骤:"
    echo ""
    echo "1. 获取 Google Cloud 项目 ID:"
    echo "   - 访问 https://console.cloud.google.com/"
    echo "   - 创建或选择项目"
    echo "   - 记录项目 ID"
    echo ""
    echo "2. 启用 Gemini API:"
    echo "   - 在 Google Cloud Console 中搜索 'Generative Language API'"
    echo "   - 点击启用"
    echo ""
    echo "3. 创建 OAuth 2.0 凭证:"
    echo "   - 转到 'APIs & Services' > 'Credentials'"
    echo "   - 创建 'OAuth 2.0 Client IDs' (Desktop application)"
    echo "   - 下载 JSON 文件"
    echo ""
    echo "4. 配置认证:"
    echo "   cp /path/to/downloaded/oauth_creds.json ~/.gemini/oauth_creds.json"
    echo "   export GOOGLE_CLOUD_PROJECT='your-project-id'"
    echo ""
    echo "5. (可选) 如果在中国大陆，设置代理:"
    echo "   export https_proxy=http://127.0.0.1:7890"
    echo "   export http_proxy=http://127.0.0.1:7890"
    echo ""
fi

# 检查项目 ID
if [ -n "$GOOGLE_CLOUD_PROJECT" ]; then
    echo "✅ 项目 ID: $GOOGLE_CLOUD_PROJECT"
elif [ -n "$GEMINI_PROJECT_ID" ]; then
    echo "✅ 项目 ID: $GEMINI_PROJECT_ID"
else
    echo "⚠️  未设置项目 ID"
    echo "请设置: export GOOGLE_CLOUD_PROJECT='your-project-id'"
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 下一步:"
echo "1. 确保已完成认证配置 (见上方提示)"
echo "2. 运行应用: ./start.sh"
echo "3. 访问: http://localhost:9000"
echo ""
echo "📚 详细文档: 查看 INSTALLATION.md"
echo "🐛 遇到问题: 查看 INSTALLATION.md 的故障排除部分"
echo ""
echo "🚀 准备就绪！运行 './start.sh' 启动应用"
