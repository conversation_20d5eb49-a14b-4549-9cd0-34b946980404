@echo off
setlocal enabledelayedexpansion

echo Debugging OAuth Authentication Detection
echo ========================================
echo.

echo Checking authentication paths and variables...
echo.

REM Show user profile
echo USERPROFILE: %USERPROFILE%
echo.

REM Check if .gemini directory exists
set GEMINI_DIR=%USERPROFILE%\.gemini
echo Checking directory: %GEMINI_DIR%
if exist "%GEMINI_DIR%" (
    echo [OK] .gemini directory exists
) else (
    echo [ERROR] .gemini directory does not exist
    echo Creating directory...
    mkdir "%GEMINI_DIR%"
    if exist "%GEMINI_DIR%" (
        echo [OK] Directory created successfully
    ) else (
        echo [ERROR] Failed to create directory
    )
)
echo.

REM Check if oauth_creds.json exists
set OAUTH_FILE=%USERPROFILE%\.gemini\oauth_creds.json
echo Checking file: %OAUTH_FILE%
if exist "%OAUTH_FILE%" (
    echo [OK] oauth_creds.json file exists
    echo File size:
    dir "%OAUTH_FILE%" | findstr oauth_creds.json
) else (
    echo [ERROR] oauth_creds.json file does not exist
    echo.
    echo Please create this file with your OAuth credentials.
    echo You can download it from Google Cloud Console:
    echo 1. Go to https://console.cloud.google.com/
    echo 2. Navigate to APIs and Services ^> Credentials
    echo 3. Create OAuth 2.0 Client ID (Desktop application)
    echo 4. Download the JSON file
    echo 5. Copy it to: %OAUTH_FILE%
)
echo.

REM Check environment variables
echo Checking environment variables...
if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo [OK] GOOGLE_APPLICATION_CREDENTIALS: %GOOGLE_APPLICATION_CREDENTIALS%
) else (
    echo [INFO] GOOGLE_APPLICATION_CREDENTIALS not set
)

if defined GEMINI_OAUTH_CREDS (
    echo [OK] GEMINI_OAUTH_CREDS: %GEMINI_OAUTH_CREDS%
) else (
    echo [INFO] GEMINI_OAUTH_CREDS not set
)

if defined GOOGLE_CLOUD_PROJECT (
    echo [OK] GOOGLE_CLOUD_PROJECT: %GOOGLE_CLOUD_PROJECT%
) else (
    echo [INFO] GOOGLE_CLOUD_PROJECT not set
)

if defined GEMINI_PROJECT_ID (
    echo [OK] GEMINI_PROJECT_ID: %GEMINI_PROJECT_ID%
) else (
    echo [INFO] GEMINI_PROJECT_ID not set
)
echo.

REM Test the same logic as start-windows.bat
echo Testing authentication detection logic...
set AUTH_FOUND=0

if exist "%USERPROFILE%\.gemini\oauth_creds.json" (
    echo [CHECK] oauth_creds.json file found
    set AUTH_FOUND=1
)

if defined GOOGLE_APPLICATION_CREDENTIALS (
    echo [CHECK] GOOGLE_APPLICATION_CREDENTIALS variable found
    set AUTH_FOUND=1
)

if defined GEMINI_OAUTH_CREDS (
    echo [CHECK] GEMINI_OAUTH_CREDS variable found
    set AUTH_FOUND=1
)

echo.
if %AUTH_FOUND%==1 (
    echo [RESULT] Authentication configuration FOUND
    echo start-windows.bat should work correctly
) else (
    echo [RESULT] Authentication configuration NOT FOUND
    echo This is why start-windows.bat is showing the warning
)

echo.
echo Debug completed!
pause
