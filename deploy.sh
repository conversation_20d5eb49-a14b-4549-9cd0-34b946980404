#!/bin/bash

echo "🚀 Gemini CLI Chat 服务器部署脚本"
echo "=================================="
echo ""

# 检查是否为 root 用户
if [ "$EUID" -eq 0 ]; then
    echo "❌ 请不要使用 root 用户运行此脚本"
    echo "建议创建普通用户: sudo adduser gemini && sudo usermod -aG sudo gemini"
    exit 1
fi

# 检查系统
if ! command -v apt &> /dev/null && ! command -v yum &> /dev/null; then
    echo "❌ 不支持的操作系统，请手动部署"
    exit 1
fi

# 获取用户输入
read -p "🔍 请输入你的域名 (例如: gemini.example.com): " DOMAIN
read -p "🔍 请输入 Google Cloud 项目 ID: " PROJECT_ID
read -p "🔍 请输入 OAuth 凭证文件路径: " OAUTH_CREDS_PATH

if [ -z "$DOMAIN" ] || [ -z "$PROJECT_ID" ] || [ -z "$OAUTH_CREDS_PATH" ]; then
    echo "❌ 所有字段都是必填的"
    exit 1
fi

if [ ! -f "$OAUTH_CREDS_PATH" ]; then
    echo "❌ OAuth 凭证文件不存在: $OAUTH_CREDS_PATH"
    exit 1
fi

echo ""
echo "📋 部署配置:"
echo "   域名: $DOMAIN"
echo "   项目ID: $PROJECT_ID"
echo "   凭证文件: $OAUTH_CREDS_PATH"
echo ""

read -p "确认开始部署? (y/N): " CONFIRM
if [ "$CONFIRM" != "y" ] && [ "$CONFIRM" != "Y" ]; then
    echo "❌ 部署已取消"
    exit 1
fi

echo ""
echo "🔧 开始部署..."

# 1. 安装基础环境
echo "📦 安装基础环境..."

if command -v apt &> /dev/null; then
    # Ubuntu/Debian
    sudo apt update
    sudo apt install -y curl wget git nginx
    
    # 安装 Node.js 18+
    if ! command -v node &> /dev/null; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        sudo apt-get install -y nodejs
    fi
elif command -v yum &> /dev/null; then
    # CentOS/RHEL
    sudo yum update -y
    sudo yum install -y curl wget git nginx
    
    # 安装 Node.js 18+
    if ! command -v node &> /dev/null; then
        curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
        sudo yum install -y nodejs
    fi
fi

# 检查 Node.js 版本
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js 版本过低 (当前: $(node -v), 需要: 18+)"
    exit 1
fi

echo "✅ Node.js $(node -v) 已安装"

# 2. 创建应用目录
echo "📁 创建应用目录..."
sudo mkdir -p /opt/gemini-cli-chat
sudo chown $USER:$USER /opt/gemini-cli-chat

# 3. 部署应用
echo "📦 部署应用..."
cd /opt/gemini-cli-chat

# 如果是 git 仓库，拉取代码；否则复制当前目录
if [ -d ".git" ]; then
    git pull origin main
else
    # 复制当前目录内容（假设脚本在项目根目录运行）
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    cp -r "$SCRIPT_DIR"/* .
fi

# 安装依赖
echo "📦 安装依赖..."
npm install
cd backend && npm install && cd ..
cd web && npm install && cd ..

# 构建项目
echo "🔨 构建项目..."
cd backend && npm run build && cd ..
cd web && npm run build && cd ..

# 4. 配置认证
echo "🔐 配置认证..."
sudo mkdir -p /etc/gemini-cli-chat
sudo cp "$OAUTH_CREDS_PATH" /etc/gemini-cli-chat/oauth_creds.json
sudo chown $USER:$USER /etc/gemini-cli-chat/oauth_creds.json

# 设置环境变量
sudo tee /etc/environment << EOF
GOOGLE_CLOUD_PROJECT=$PROJECT_ID
GEMINI_OAUTH_CREDS=/etc/gemini-cli-chat/oauth_creds.json
NODE_ENV=production
EOF

# 5. 创建系统服务
echo "⚙️  创建系统服务..."

# 后端服务
sudo tee /etc/systemd/system/gemini-backend.service << EOF
[Unit]
Description=Gemini CLI Chat Backend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/gemini-cli-chat/backend
Environment=NODE_ENV=production
Environment=GOOGLE_CLOUD_PROJECT=$PROJECT_ID
Environment=GEMINI_OAUTH_CREDS=/etc/gemini-cli-chat/oauth_creds.json
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 安装 serve 用于前端
sudo npm install -g serve

# 前端服务
sudo tee /etc/systemd/system/gemini-frontend.service << EOF
[Unit]
Description=Gemini CLI Chat Frontend
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/gemini-cli-chat/web
ExecStart=/usr/bin/serve -s dist -l 3000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable gemini-backend gemini-frontend
sudo systemctl start gemini-backend gemini-frontend

# 6. 配置 Nginx
echo "🌐 配置 Nginx..."

sudo tee /etc/nginx/sites-available/gemini-cli-chat << EOF
server {
    listen 80;
    server_name $DOMAIN;

    # 前端静态文件
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # 后端 API
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_buffering off;
        proxy_cache off;
    }

    # OpenAI 兼容 API
    location /v1/ {
        proxy_pass http://localhost:8000/v1/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_buffering off;
        proxy_cache off;
    }
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/gemini-cli-chat /etc/nginx/sites-enabled/
sudo nginx -t

if [ $? -eq 0 ]; then
    sudo systemctl restart nginx
    echo "✅ Nginx 配置成功"
else
    echo "❌ Nginx 配置失败"
    exit 1
fi

# 7. 配置防火墙
echo "🔒 配置防火墙..."
if command -v ufw &> /dev/null; then
    sudo ufw allow ssh
    sudo ufw allow 80
    sudo ufw allow 443
    sudo ufw --force enable
elif command -v firewall-cmd &> /dev/null; then
    sudo firewall-cmd --permanent --add-service=ssh
    sudo firewall-cmd --permanent --add-service=http
    sudo firewall-cmd --permanent --add-service=https
    sudo firewall-cmd --reload
fi

# 8. 验证部署
echo "🔍 验证部署..."
sleep 5

# 检查服务状态
if systemctl is-active --quiet gemini-backend; then
    echo "✅ 后端服务运行正常"
else
    echo "❌ 后端服务启动失败"
    sudo systemctl status gemini-backend
fi

if systemctl is-active --quiet gemini-frontend; then
    echo "✅ 前端服务运行正常"
else
    echo "❌ 前端服务启动失败"
    sudo systemctl status gemini-frontend
fi

if systemctl is-active --quiet nginx; then
    echo "✅ Nginx 服务运行正常"
else
    echo "❌ Nginx 服务启动失败"
    sudo systemctl status nginx
fi

# 测试 API
echo "🧪 测试 API..."
if curl -s http://localhost:8000/v1/models > /dev/null; then
    echo "✅ API 响应正常"
else
    echo "❌ API 无响应"
fi

echo ""
echo "🎉 部署完成！"
echo "=================================="
echo "📱 Web 界面: http://$DOMAIN"
echo "🔌 API 端点: http://$DOMAIN/v1"
echo ""
echo "📋 后续步骤:"
echo "1. 配置 DNS 将域名指向此服务器"
echo "2. 配置 SSL 证书: sudo certbot --nginx -d $DOMAIN"
echo "3. 检查服务状态: sudo systemctl status gemini-backend gemini-frontend"
echo "4. 查看日志: sudo journalctl -u gemini-backend -f"
echo ""
echo "🔧 管理命令:"
echo "- 重启服务: sudo systemctl restart gemini-backend gemini-frontend"
echo "- 查看日志: sudo journalctl -u gemini-backend -f"
echo "- 更新应用: cd /opt/gemini-cli-chat && git pull && ./deploy.sh"
echo ""
echo "✅ 部署成功完成！"
