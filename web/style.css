* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
    color: #4a5568;
    font-size: 1.5rem;
    font-weight: 600;
}

.model-selector,
.mode-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.model-selector label,
.mode-selector label {
    font-weight: 500;
    color: #4a5568;
}

.model-selector select,
.mode-selector select {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
}

.model-selector select:focus,
.mode-selector select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.tool-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.tool-btn:hover {
    border-color: #667eea;
    background: #f7fafc;
    color: #667eea;
}

.tool-btn:active {
    transform: translateY(1px);
}

.embed-model-select {
    padding: 0.4rem 0.8rem;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    background: white;
    color: #4a5568;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: 0.5rem;
    min-width: 180px;
}

.embed-model-select:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.embed-model-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.main {
    flex: 1;
    display: flex;
    gap: 1rem;
    padding: 1rem;
    overflow: hidden;
}

.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.message {
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    text-align: right;
}

.message.assistant {
    text-align: left;
}

.message.system {
    text-align: center;
    opacity: 0.7;
}

.message-content {
    display: inline-block;
    max-width: 80%;
    padding: 1rem 1.5rem;
    border-radius: 18px;
    word-wrap: break-word;
    line-height: 1.5;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
    background: #f7fafc;
    color: #2d3748;
    border: 1px solid #e2e8f0;
    border-bottom-left-radius: 4px;
}

.message.system .message-content {
    background: #fed7d7;
    color: #c53030;
    border-radius: 8px;
    font-size: 0.9rem;
}

.message-content pre {
    background: #2d3748;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 8px;
    overflow-x: auto;
    margin: 0.5rem 0;
}

.message-content code {
    background: #edf2f7;
    color: #2d3748;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: "Monaco", "Menlo", monospace;
    font-size: 0.9em;
}

.input-container {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    background: rgba(255, 255, 255, 0.5);
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.input-row {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.tool-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

#message-input {
    flex: 1;
    padding: 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    resize: vertical;
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.2s;
    background: white;
}

#message-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.send-btn {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
    min-width: 100px;
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.send-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.stop-btn {
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
    color: white;
    border: none;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
    min-width: 80px;
}

.stop-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(229, 62, 62, 0.3);
}

.stop-btn:active {
    transform: translateY(0);
}

.sidebar {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow-y: auto;
    max-height: 100vh;
    padding: 1rem;
}

.stats,
.controls,
.features {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stats h3,
.controls h3,
.features h3 {
    margin-bottom: 1rem;
    color: #4a5568;
    font-size: 1.1rem;
}

.features h4 {
    margin: 1rem 0 0.5rem 0;
    color: #667eea;
    font-size: 0.95rem;
    font-weight: 600;
}

.features h4:first-of-type {
    margin-top: 0.5rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.control-btn {
    width: 100%;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
}

.control-btn:hover {
    border-color: #667eea;
    background: #f7fafc;
}

.control-btn:last-child {
    margin-bottom: 0;
}

.typing-speed-control {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.typing-speed-control label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4a5568;
    font-size: 0.9rem;
}

.typing-speed-control input[type="range"] {
    width: 100%;
    margin-bottom: 0.5rem;
    accent-color: #667eea;
}

.typing-speed-control span {
    font-size: 0.8rem;
    color: #667eea;
    font-weight: 500;
}

.features ul {
    list-style: none;
}

.features li {
    margin-bottom: 0.75rem;
    padding-left: 1rem;
    position: relative;
    line-height: 1.4;
}

.features li:before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.streaming-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #48bb78;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
    margin-left: 0.5rem;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.typing-effect {
    border-right: 2px solid #667eea;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%,
    50% {
        border-color: #667eea;
    }
    51%,
    100% {
        border-color: transparent;
    }
}

.typing-text {
    display: inline-block;
    overflow: hidden;
}

.token-info {
    background: #e6fffa;
    border: 1px solid #81e6d9;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
    font-family: "Monaco", "Menlo", monospace;
    font-size: 0.9rem;
}

.embed-info {
    background: #fef5e7;
    border: 1px solid #f6e05e;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
    font-family: "Monaco", "Menlo", monospace;
    font-size: 0.9rem;
    max-height: 200px;
    overflow-y: auto;
}

/* 配置面板模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--bg-color);
    margin: 5% auto;
    padding: 0;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px 12px 0 0;
}

.modal-header h2 {
    margin: 0;
    color: white;
    font-size: 1.5em;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #ff6b6b;
}

.modal-body {
    padding: 20px;
}

.current-status {
    margin-bottom: 25px;
    padding: 15px;
    background-color: var(--message-bg);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.current-status h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.action-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.action-btn {
    flex: 1;
    min-width: 150px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn.primary {
    background: linear-gradient(135deg, #4285f4, #34a853);
    color: white;
}

.action-btn.primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
}

.action-btn.success {
    background: linear-gradient(135deg, #34a853, #0f9d58);
    color: white;
}

.action-btn.success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 168, 83, 0.3);
}

.action-btn.danger {
    background: linear-gradient(135deg, #ea4335, #d33b2c);
    color: white;
}

.action-btn.danger:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(234, 67, 53, 0.3);
}

.saved-accounts {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
}

.saved-accounts h3 {
    margin-top: 0;
    color: var(--primary-color);
}

.account-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background-color: var(--message-bg);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.account-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
}

.account-info {
    flex: 1;
}

.account-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

.account-project {
    font-size: 0.9em;
    color: var(--text-secondary);
}

.account-actions {
    display: flex;
    gap: 8px;
}

.account-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.account-btn.switch {
    background-color: var(--primary-color);
    color: white;
}

.account-btn.switch:hover {
    background-color: var(--secondary-color);
}

.account-btn.delete {
    background-color: #ea4335;
    color: white;
}

.account-btn.delete:hover {
    background-color: #d33b2c;
}

.account-btn:disabled {
    background: #e2e8f0;
    color: #a0aec0;
    cursor: not-allowed;
}

.account-btn:disabled:hover {
    background: #e2e8f0;
    color: #a0aec0;
}

.current-badge {
    display: inline-block;
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    margin-left: 0.5rem;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.current-account {
    border: 2px solid #48bb78;
    background: linear-gradient(135deg, #f0fff4, #e6fffa);
}

.current-account .account-name {
    color: #2d3748;
    font-weight: 600;
}

.empty-message {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 20px;
}

.status-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-icon {
    font-size: 1.2em;
}

.status-text {
    flex: 1;
}

/* 平板设备适配 */
@media (max-width: 1024px) {
    .sidebar {
        width: 280px;
    }

    .stats,
    .controls,
    .features {
        min-width: 220px;
    }
}

/* 小屏幕设备适配 */
@media (max-width: 768px) {
    .container {
        height: 100vh;
        overflow: hidden;
    }

    .main {
        flex-direction: column;
        height: 100%;
    }

    .main-content {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
    }

    .header {
        flex-wrap: wrap;
        gap: 0.5rem;
        padding: 0.75rem;
        min-height: auto;
    }

    .header h1 {
        font-size: 1.2rem;
        margin: 0;
    }

    .header-controls {
        gap: 0.5rem;
    }

    .header-controls select {
        padding: 0.4rem 0.6rem;
        font-size: 0.85rem;
    }

    .chat-container {
        flex: 1;
        min-height: 0;
        display: flex;
        flex-direction: column;
    }

    .messages {
        flex: 1;
        min-height: 0;
        padding: 0.75rem;
    }

    .input-container {
        padding: 0.75rem;
        gap: 0.5rem;
    }

    .message-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 0.75rem;
    }

    .send-btn,
    .stop-btn {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .tool-buttons {
        gap: 0.5rem;
    }

    .tool-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .sidebar {
        width: 100%;
        max-height: 40vh;
        flex-direction: row;
        overflow-x: auto;
        overflow-y: hidden;
        padding: 0.75rem;
        gap: 1rem;
        border-left: none;
        border-top: 1px solid var(--border-color);
    }

    .stats,
    .controls,
    .features {
        min-width: 250px;
        flex-shrink: 0;
    }

    .stats h3,
    .controls h3,
    .features h3 {
        font-size: 1rem;
        margin-bottom: 0.75rem;
    }

    .control-btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
    }

    .typing-speed-control {
        margin-top: 0.75rem;
    }

    .typing-speed-control label {
        font-size: 0.85rem;
    }

    /* 配置面板移动端适配 */
    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 90vh;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-header h2 {
        font-size: 1.2rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }

    .action-btn {
        min-width: auto;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }

    .account-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem;
    }

    .account-actions {
        width: 100%;
        justify-content: flex-end;
        gap: 0.5rem;
    }

    .account-btn {
        padding: 0.5rem 1rem;
        font-size: 0.85rem;
    }
}

/* 超小屏幕设备适配 */
@media (max-width: 480px) {
    .header {
        padding: 0.5rem;
    }

    .header h1 {
        font-size: 1rem;
    }

    .header-controls {
        width: 100%;
        justify-content: space-between;
    }

    .header-controls select {
        flex: 1;
        max-width: 120px;
    }

    .messages {
        padding: 0.5rem;
    }

    .input-container {
        padding: 0.5rem;
        flex-direction: column;
    }

    .input-row {
        width: 100%;
    }

    .tool-buttons {
        width: 100%;
        justify-content: space-between;
        margin-top: 0.5rem;
    }

    .tool-btn {
        flex: 1;
        margin: 0 0.25rem;
        padding: 0.5rem;
        font-size: 0.75rem;
    }

    .sidebar {
        max-height: 35vh;
        padding: 0.5rem;
    }

    .stats,
    .controls,
    .features {
        min-width: 220px;
    }

    .control-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .modal-content {
        width: 98%;
        margin: 1% auto;
    }

    .modal-header {
        padding: 0.75rem;
    }

    .modal-body {
        padding: 0.75rem;
    }
}
