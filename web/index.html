<!DOCTYPE html>
<html lang="zh-CN">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
        <title>Gemini CLI Chat</title>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css" />
        <link rel="stylesheet" href="./style.css" />
    </head>
    <body>
        <div id="app">
            <header class="header">
                <h1>🤖 Gemini CLI Chat</h1>
                <div class="model-selector">
                    <label for="model-select">模型:</label>
                    <select id="model-select">
                        <optgroup label="🚀 Gemini 2.5 (推荐)">
                            <option value="gemini-2.5-flash" selected>Gemini 2.5 Flash (快速)</option>
                            <option value="gemini-2.5-pro">Gemini 2.5 Pro (专业)</option>
                            <option value="gemini-2.5-flash-lite-preview-06-17">Gemini 2.5 Flash Lite (轻量)</option>
                        </optgroup>
                        <optgroup label="⚡ Gemini 2.0">
                            <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                            <option value="gemini-2.0-flash-live-001">Gemini 2.0 Flash Live</option>
                        </optgroup>
                        <optgroup label="📚 Gemini 1.5">
                            <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                            <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                        </optgroup>
                        <optgroup label="🎓 LearnLM (学习专用)">
                            <option value="learnlm-2.0-flash-experimental">LearnLM 2.0 Flash</option>
                            <option value="learnlm-1.5-pro-experimental">LearnLM 1.5 Pro</option>
                        </optgroup>
                    </select>
                </div>
                <div class="mode-selector">
                    <label for="mode-select">对话模式:</label>
                    <select id="mode-select">
                        <option value="stream">流式对话</option>
                        <option value="normal">普通对话</option>
                    </select>
                </div>
            </header>

            <main class="main">
                <div class="chat-container">
                    <div id="messages" class="messages"></div>
                    <div class="input-container">
                        <div class="input-row">
                            <textarea id="message-input" placeholder="输入你的消息..." rows="3"></textarea>
                            <button id="send-btn" class="send-btn">
                                <span class="send-text">发送</span>
                                <span class="loading" style="display: none">发送中...</span>
                            </button>
                            <button id="stop-btn" class="stop-btn" style="display: none">
                                <span>⏹️ 停止</span>
                            </button>
                        </div>
                        <div class="tool-buttons">
                            <button id="token-tool" class="tool-btn">Token计数</button>
                            <button id="embed-tool" class="tool-btn">内容嵌入</button>
                            <select id="embed-model-select" class="embed-model-select">
                                <option value="text-embedding-004">text-embedding-004 (推荐)</option>
                                <option value="text-embedding-005">text-embedding-005</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="sidebar">
                    <div class="stats">
                        <h3>📊 统计信息</h3>
                        <div class="stat-item">
                            <span>总消息数:</span>
                            <span id="message-count">0</span>
                        </div>
                        <div class="stat-item">
                            <span>总Token数:</span>
                            <span id="token-count">0</span>
                        </div>
                    </div>

                    <div class="controls">
                        <h3>🛠️ 控制面板</h3>
                        <button id="clear-btn" class="control-btn">清空对话</button>
                        <button id="export-btn" class="control-btn">导出对话</button>
                        <button id="test-connection" class="control-btn">测试连接</button>
                        <button id="config-panel-btn" class="control-btn">账户配置</button>

                        <div class="typing-speed-control">
                            <label for="typing-speed">打字速度:</label>
                            <input type="range" id="typing-speed" min="10" max="100" value="30" step="10" />
                            <span id="speed-value">30ms</span>
                        </div>
                    </div>

                    <div class="features">
                        <h3>✨ 功能说明</h3>
                        <h4>🤖 可用模型</h4>
                        <ul>
                            <li><strong>Gemini 2.5:</strong> 最新最强模型</li>
                            <li><strong>Gemini 2.0:</strong> 支持实时API</li>
                            <li><strong>Gemini 1.5:</strong> 稳定可靠</li>
                            <li><strong>LearnLM:</strong> 学习教育专用</li>
                        </ul>
                        <h4>💬 对话模式</h4>
                        <ul>
                            <li><strong>流式对话:</strong> 实时显示AI回复</li>
                            <li><strong>普通对话:</strong> 一次性获取完整回复</li>
                        </ul>
                        <h4>🛠️ 工具功能</h4>
                        <ul>
                            <li><strong>Token计数:</strong> 计算消息的Token数量</li>
                            <li><strong>内容嵌入:</strong> 获取文本的向量表示</li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>

        <!-- 账户配置面板 -->
        <div id="config-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>👥 账户配置管理</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <!-- 当前登录状态 -->
                    <div class="current-status">
                        <h3>📊 当前登录状态</h3>
                        <div id="current-login-info">
                            <p>正在检查登录状态...</p>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <button id="get-login-info" class="action-btn primary">🔍 获取登录信息</button>
                        <button id="save-login-info" class="action-btn success" disabled>💾 保存登录信息</button>
                        <button id="clear-login-info" class="action-btn danger">🗑️ 清除登录信息</button>
                    </div>

                    <!-- 保存的账户列表 -->
                    <div class="saved-accounts">
                        <h3>📋 已保存的账户</h3>
                        <div id="accounts-list">
                            <p class="empty-message">暂无保存的账户</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script type="module" src="./main.js"></script>
    </body>
</html>
