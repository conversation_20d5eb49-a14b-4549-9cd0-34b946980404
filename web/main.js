import { marked } from "marked";
import hljs from "highlight.js";

// 配置marked
marked.setOptions({
    highlight: function (code, lang) {
        if (lang && hljs.getLanguage(lang)) {
            try {
                return hljs.highlight(code, { language: lang }).value;
            } catch (err) {}
        }
        return hljs.highlightAuto(code).value;
    },
    breaks: true,
    gfm: true
});

class GeminiChat {
    constructor() {
        this.messages = [];
        this.messageCount = 0;
        this.totalTokens = 0;
        this.isStreaming = false;
        this.abortController = null;
        this.typingInterval = null;
        this.typingSpeed = 30; // 打字速度 (毫秒)
        this.currentTypingText = "";
        this.targetText = "";
        this.currentMessageEl = null;

        this.initElements();
        this.bindEvents();
        this.addSystemMessage("欢迎使用 Gemini CLI Chat! 🎉");
    }

    initElements() {
        this.messagesContainer = document.getElementById("messages");
        this.messageInput = document.getElementById("message-input");
        this.sendBtn = document.getElementById("send-btn");
        this.stopBtn = document.getElementById("stop-btn");
        this.modelSelect = document.getElementById("model-select");
        this.modeSelect = document.getElementById("mode-select");
        this.tokenToolBtn = document.getElementById("token-tool");
        this.embedToolBtn = document.getElementById("embed-tool");
        this.embedModelSelect = document.getElementById("embed-model-select");
        this.messageCountEl = document.getElementById("message-count");
        this.tokenCountEl = document.getElementById("token-count");
        this.clearBtn = document.getElementById("clear-btn");
        this.exportBtn = document.getElementById("export-btn");
        this.testConnectionBtn = document.getElementById("test-connection");
        this.typingSpeedSlider = document.getElementById("typing-speed");
        this.speedValueEl = document.getElementById("speed-value");
    }

    bindEvents() {
        this.sendBtn.addEventListener("click", () => this.sendMessage());
        this.messageInput.addEventListener("keydown", (e) => {
            if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 停止按钮事件
        this.stopBtn.addEventListener("click", () => this.stopStreaming());

        // 工具按钮事件
        this.tokenToolBtn.addEventListener("click", () => this.handleTokenTool());
        this.embedToolBtn.addEventListener("click", () => this.handleEmbedTool());

        this.clearBtn.addEventListener("click", () => this.clearMessages());
        this.exportBtn.addEventListener("click", () => this.exportChat());
        this.testConnectionBtn.addEventListener("click", () => this.testConnection());

        // 打字速度控制
        this.typingSpeedSlider.addEventListener("input", (e) => {
            this.typingSpeed = parseInt(e.target.value);
            this.speedValueEl.textContent = `${this.typingSpeed}ms`;
        });
    }

    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isStreaming) return;

        const model = this.modelSelect.value;
        const mode = this.modeSelect.value;

        this.addUserMessage(message);
        this.messageInput.value = "";
        this.setLoading(true);

        try {
            switch (mode) {
                case "stream":
                    await this.handleStreamChat(model, message);
                    break;
                case "normal":
                    await this.handleNormalChat(model, message);
                    break;
            }
        } catch (error) {
            this.addSystemMessage(`错误: ${error.message}`, "error");
        } finally {
            this.setLoading(false);
        }
    }

    async handleTokenTool() {
        const message = this.messageInput.value.trim();
        if (!message) {
            this.addSystemMessage("请先输入要计算Token的文本", "error");
            return;
        }

        const model = this.modelSelect.value;
        this.setLoading(true);

        try {
            await this.handleTokenCount(model, message);
            this.messageInput.value = "";
        } catch (error) {
            this.addSystemMessage(`Token计数错误: ${error.message}`, "error");
        } finally {
            this.setLoading(false);
        }
    }

    async handleEmbedTool() {
        const message = this.messageInput.value.trim();
        if (!message) {
            this.addSystemMessage("请先输入要嵌入的文本", "error");
            return;
        }

        this.setLoading(true);

        try {
            await this.handleEmbed(message);
            this.messageInput.value = "";
        } catch (error) {
            this.addSystemMessage(`内容嵌入错误: ${error.message}`, "error");
        } finally {
            this.setLoading(false);
        }
    }

    async handleStreamChat(model, message) {
        this.isStreaming = true;
        this.abortController = new AbortController();
        const assistantMessage = this.addAssistantMessage("", true);
        this.showStopButton();

        try {
            const response = await fetch(`/api/${model}:streamGenerateContent`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    contents: [
                        {
                            role: "user",
                            parts: [{ text: message }]
                        }
                    ]
                }),
                signal: this.abortController.signal
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let buffer = "";
            let fullText = "";

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split("\n");
                buffer = lines.pop(); // 保留不完整的行

                for (const line of lines) {
                    if (line.startsWith("data: ")) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                                const text = data.candidates[0].content.parts[0].text;
                                fullText += text;
                                await this.typeText(assistantMessage, fullText);
                            }
                        } catch (e) {
                            console.warn("解析SSE数据失败:", e);
                        }
                    }
                }
            }
        } catch (error) {
            if (error.name === "AbortError") {
                this.addSystemMessage("流式传输已停止", "info");
            } else {
                throw error;
            }
        } finally {
            this.isStreaming = false;
            this.abortController = null;
            this.hideStopButton();
            this.stopTyping();
            this.removeStreamingIndicator(assistantMessage);
            this.clearTypingEffect(assistantMessage);
        }
    }

    stopStreaming() {
        if (this.abortController) {
            this.abortController.abort();
        }
        this.stopTyping();
    }

    showStopButton() {
        this.stopBtn.style.display = "inline-block";
        this.sendBtn.style.display = "none";
    }

    hideStopButton() {
        this.stopBtn.style.display = "none";
        this.sendBtn.style.display = "inline-block";
    }

    async typeText(messageEl, fullText) {
        // 更新目标文本
        this.targetText = fullText;
        this.currentMessageEl = messageEl;

        // 如果没有正在进行的打字动画，开始新的
        if (!this.typingInterval) {
            this.startTyping();
        }
    }

    startTyping() {
        const contentEl = this.currentMessageEl.querySelector(".message-content");
        contentEl.classList.add("typing-effect");

        this.typingInterval = setInterval(() => {
            // 如果当前文本长度小于目标文本，继续打字
            if (this.currentTypingText.length < this.targetText.length) {
                this.currentTypingText = this.targetText.substring(0, this.currentTypingText.length + 1);

                // 更新显示内容
                contentEl.innerHTML = marked(this.currentTypingText);

                // 保持流式指示器
                if (this.isStreaming) {
                    const indicator = document.createElement("span");
                    indicator.className = "streaming-indicator";
                    contentEl.appendChild(indicator);
                }

                this.scrollToBottom();
            } else {
                // 如果已经打完当前目标文本，暂停打字但保持interval
                // 等待新的文本chunk到达
            }
        }, this.typingSpeed);
    }

    stopTyping() {
        if (this.typingInterval) {
            clearInterval(this.typingInterval);
            this.typingInterval = null;
        }

        // 立即显示完整文本
        if (this.currentMessageEl && this.targetText) {
            const contentEl = this.currentMessageEl.querySelector(".message-content");
            contentEl.innerHTML = marked(this.targetText);
            this.clearTypingEffect(this.currentMessageEl);
        }

        // 重置状态
        this.currentTypingText = "";
        this.targetText = "";
        this.currentMessageEl = null;
    }

    clearTypingEffect(messageEl) {
        const contentEl = messageEl.querySelector(".message-content");
        if (contentEl) {
            contentEl.classList.remove("typing-effect");
        }
    }

    async handleNormalChat(model, message) {
        const response = await fetch(`/api/${model}:generateContent`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                contents: [
                    {
                        role: "user",
                        parts: [{ text: message }]
                    }
                ]
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        if (data.candidates && data.candidates[0] && data.candidates[0].content) {
            const text = data.candidates[0].content.parts[0].text;
            this.addAssistantMessage(text);

            // 更新token统计
            if (data.usageMetadata) {
                this.totalTokens += data.usageMetadata.totalTokenCount || 0;
                this.updateStats();
            }
        } else {
            throw new Error("无效的响应格式");
        }
    }

    async handleTokenCount(model, message) {
        const response = await fetch(`/api/${model}:countTokens`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                contents: [
                    {
                        role: "user",
                        parts: [{ text: message }]
                    }
                ]
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        const tokenInfo = `
**Token 计数结果:**
- 总 Token 数: ${data.totalTokens}
- 消息内容: "${message}"
- 模型: ${model}
        `.trim();

        this.addAssistantMessage(tokenInfo, false, "token-info");
    }

    async handleEmbed(message) {
        const embedModel = this.embedModelSelect.value;
        const response = await fetch(`/api/${embedModel}:embedContent`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify({
                contents: [
                    {
                        role: "user",
                        parts: [{ text: message }]
                    }
                ]
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        if (data.embedding && data.embedding.values) {
            const embedInfo = `
**内容嵌入结果:**
- 嵌入模型: ${embedModel}
- 向量维度: ${data.embedding.values.length}
- 消息内容: "${message}"
- 向量预览: [${data.embedding.values
                .slice(0, 5)
                .map((v) => v.toFixed(4))
                .join(", ")}...]
            `.trim();

            this.addAssistantMessage(embedInfo, false, "embed-info");
        } else {
            throw new Error("无效的嵌入响应格式");
        }
    }

    addUserMessage(text) {
        this.messages.push({ role: "user", content: text });
        this.messageCount++;
        this.updateStats();

        const messageEl = this.createMessageElement("user", text);
        this.messagesContainer.appendChild(messageEl);
        this.scrollToBottom();
    }

    addAssistantMessage(text, isStreaming = false, className = "") {
        this.messages.push({ role: "assistant", content: text });
        this.messageCount++;
        this.updateStats();

        const messageEl = this.createMessageElement("assistant", text, isStreaming, className);
        this.messagesContainer.appendChild(messageEl);
        this.scrollToBottom();

        return messageEl;
    }

    addSystemMessage(text, type = "info") {
        const messageEl = this.createMessageElement("system", text);
        if (type === "error") {
            messageEl.querySelector(".message-content").style.background = "#fed7d7";
            messageEl.querySelector(".message-content").style.color = "#c53030";
        }
        this.messagesContainer.appendChild(messageEl);
        this.scrollToBottom();
    }

    createMessageElement(role, content, isStreaming = false, className = "") {
        const messageEl = document.createElement("div");
        messageEl.className = `message ${role}`;

        const contentEl = document.createElement("div");
        contentEl.className = `message-content ${className}`;

        if (role === "assistant" && !className) {
            contentEl.innerHTML = marked(content);
        } else {
            contentEl.textContent = content;
        }

        if (isStreaming) {
            const indicator = document.createElement("span");
            indicator.className = "streaming-indicator";
            contentEl.appendChild(indicator);
        }

        messageEl.appendChild(contentEl);
        return messageEl;
    }

    updateAssistantMessage(messageEl, text) {
        const contentEl = messageEl.querySelector(".message-content");
        contentEl.innerHTML = marked(text);

        // 保持流式指示器
        if (this.isStreaming) {
            const indicator = document.createElement("span");
            indicator.className = "streaming-indicator";
            contentEl.appendChild(indicator);
        }

        this.scrollToBottom();
    }

    removeStreamingIndicator(messageEl) {
        const indicator = messageEl.querySelector(".streaming-indicator");
        if (indicator) {
            indicator.remove();
        }
    }

    setLoading(loading) {
        this.sendBtn.disabled = loading;
        const sendText = this.sendBtn.querySelector(".send-text");
        const loadingText = this.sendBtn.querySelector(".loading");

        if (loading) {
            sendText.style.display = "none";
            loadingText.style.display = "inline";
        } else {
            sendText.style.display = "inline";
            loadingText.style.display = "none";
        }
    }

    updateStats() {
        this.messageCountEl.textContent = this.messageCount;
        this.tokenCountEl.textContent = this.totalTokens;
    }

    clearMessages() {
        if (confirm("确定要清空所有对话吗？")) {
            this.messagesContainer.innerHTML = "";
            this.messages = [];
            this.messageCount = 0;
            this.totalTokens = 0;
            this.updateStats();
            this.addSystemMessage("对话已清空 🧹");
        }
    }

    exportChat() {
        const chatData = {
            timestamp: new Date().toISOString(),
            messages: this.messages,
            stats: {
                messageCount: this.messageCount,
                totalTokens: this.totalTokens
            }
        };

        const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: "application/json" });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `gemini-chat-${new Date().toISOString().split("T")[0]}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.addSystemMessage("对话已导出 📁");
    }

    async testConnection() {
        try {
            const response = await fetch("/api/gemini-2.5-flash:countTokens", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    contents: [
                        {
                            role: "user",
                            parts: [{ text: "test" }]
                        }
                    ]
                })
            });

            if (response.ok) {
                this.addSystemMessage("✅ 连接测试成功！后端服务正常运行。");
            } else {
                this.addSystemMessage(`❌ 连接测试失败: HTTP ${response.status}`, "error");
            }
        } catch (error) {
            this.addSystemMessage(`❌ 连接测试失败: ${error.message}`, "error");
        }
    }

    scrollToBottom() {
        setTimeout(() => {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }, 100);
    }
}

// 配置面板管理
class ConfigPanel {
    constructor() {
        this.modal = document.getElementById("config-modal");
        this.closeBtn = this.modal.querySelector(".close");
        this.currentLoginInfo = document.getElementById("current-login-info");
        this.getLoginInfoBtn = document.getElementById("get-login-info");
        this.saveLoginInfoBtn = document.getElementById("save-login-info");
        this.clearLoginInfoBtn = document.getElementById("clear-login-info");
        this.accountsList = document.getElementById("accounts-list");

        this.currentLoginData = null;

        this.initEventListeners();
    }

    initEventListeners() {
        // 打开配置面板
        document.getElementById("config-panel-btn").addEventListener("click", () => {
            this.openModal();
        });

        // 关闭配置面板
        this.closeBtn.addEventListener("click", () => {
            this.closeModal();
        });

        // 点击模态框外部关闭
        this.modal.addEventListener("click", (e) => {
            if (e.target === this.modal) {
                this.closeModal();
            }
        });

        // 获取登录信息
        this.getLoginInfoBtn.addEventListener("click", () => {
            this.getCurrentLoginInfo();
        });

        // 保存登录信息
        this.saveLoginInfoBtn.addEventListener("click", () => {
            this.saveCurrentLogin();
        });

        // 清除登录信息
        this.clearLoginInfoBtn.addEventListener("click", () => {
            this.clearCurrentLogin();
        });
    }

    openModal() {
        this.modal.style.display = "block";
        this.loadAccountsList();
        this.getCurrentLoginInfo();
    }

    closeModal() {
        this.modal.style.display = "none";
    }

    async getCurrentLoginInfo() {
        try {
            this.currentLoginInfo.innerHTML = "<p>🔄 正在检查登录状态...</p>";

            const response = await fetch("/api/config/current");
            const data = await response.json();

            if (data.success) {
                if (data.isLoggedIn) {
                    this.currentLoginData = data;
                    this.currentLoginInfo.innerHTML = `
                        <div class="status-info">
                            <div class="status-item">
                                <span class="status-icon">✅</span>
                                <span class="status-text">找到项目ID: ${data.projectId}</span>
                            </div>
                        </div>
                    `;
                    this.saveLoginInfoBtn.disabled = false;
                } else {
                    this.currentLoginData = null;
                    this.currentLoginInfo.innerHTML = `
                        <div class="status-info">
                            <div class="status-item">
                                <span class="status-icon">❌</span>
                                <span class="status-text">未登录</span>
                            </div>
                            <div class="status-item">
                                <span class="status-icon">💡</span>
                                <span class="status-text">${data.message}</span>
                            </div>
                        </div>
                    `;
                    this.saveLoginInfoBtn.disabled = true;
                }
            } else {
                throw new Error(data.error);
            }
        } catch (error) {
            this.currentLoginInfo.innerHTML = `
                <div class="status-info">
                    <div class="status-item">
                        <span class="status-icon">❌</span>
                        <span class="status-text">检查失败: ${error.message}</span>
                    </div>
                </div>
            `;
            this.saveLoginInfoBtn.disabled = true;
        }
    }

    async saveCurrentLogin() {
        if (!this.currentLoginData) {
            alert("当前没有有效的登录信息");
            return;
        }

        const accountName = prompt("请输入账户名称:");
        if (!accountName) {
            return;
        }

        try {
            const response = await fetch("/api/config/save", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ accountName })
            });

            const data = await response.json();

            if (data.success) {
                alert(`✅ ${data.message}`);
                this.loadAccountsList();
            } else {
                alert(`❌ 保存失败: ${data.error}`);
            }
        } catch (error) {
            alert(`❌ 保存失败: ${error.message}`);
        }
    }

    async clearCurrentLogin() {
        if (!confirm("确定要清除当前登录信息吗？这将删除OAuth凭证文件并清除环境变量。")) {
            return;
        }

        try {
            const response = await fetch("/api/config/clear", {
                method: "POST"
            });

            const data = await response.json();

            if (data.success) {
                alert(`✅ ${data.message}`);
                this.getCurrentLoginInfo();
                this.loadAccountsList();
            } else {
                alert(`❌ 清除失败: ${data.error}`);
            }
        } catch (error) {
            alert(`❌ 清除失败: ${error.message}`);
        }
    }

    async loadAccountsList() {
        try {
            // 获取账户列表和当前登录信息
            const [accountsResponse, currentResponse] = await Promise.all([fetch("/api/config/accounts"), fetch("/api/config/current")]);

            const accountsData = await accountsResponse.json();
            const currentData = await currentResponse.json();

            // 获取当前使用的项目ID
            const currentProjectId = currentData.success && currentData.isLoggedIn ? currentData.projectId : null;

            if (accountsData.success) {
                if (accountsData.accounts.length === 0) {
                    this.accountsList.innerHTML = '<p class="empty-message">暂无保存的账户</p>';
                } else {
                    this.accountsList.innerHTML = accountsData.accounts
                        .map((account) => {
                            const isCurrentAccount = currentProjectId && account.projectId === currentProjectId;
                            const currentBadge = isCurrentAccount ? '<span class="current-badge">当前使用</span>' : "";

                            return `
                        <div class="account-item ${isCurrentAccount ? "current-account" : ""}">
                            <div class="account-info">
                                <div class="account-name">📱 ${account.name} ${currentBadge}</div>
                                <div class="account-project">🆔 项目ID: ${account.projectId}</div>
                            </div>
                            <div class="account-actions">
                                <button class="account-btn switch" onclick="configPanel.switchAccount('${account.name}')" ${isCurrentAccount ? "disabled" : ""}>
                                    ${isCurrentAccount ? "已使用" : "切换"}
                                </button>
                                <button class="account-btn delete" onclick="configPanel.deleteAccount('${account.name}')">
                                    删除
                                </button>
                            </div>
                        </div>
                    `;
                        })
                        .join("");
                }
            } else {
                this.accountsList.innerHTML = `<p class="empty-message">加载失败: ${accountsData.error}</p>`;
            }
        } catch (error) {
            this.accountsList.innerHTML = `<p class="empty-message">加载失败: ${error.message}</p>`;
        }
    }

    async switchAccount(accountName) {
        if (!confirm(`确定要切换到账户 "${accountName}" 吗？这需要重启服务器才能生效。`)) {
            return;
        }

        try {
            const response = await fetch("/api/config/switch", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({ accountName })
            });

            const data = await response.json();

            if (data.success) {
                alert(`✅ ${data.message}`);
                if (data.needRestart) {
                    alert("⚠️ 请重启服务器以使账户切换生效");
                }
            } else {
                alert(`❌ 切换失败: ${data.error}`);
            }
        } catch (error) {
            alert(`❌ 切换失败: ${error.message}`);
        }
    }

    async deleteAccount(accountName) {
        if (!confirm(`确定要删除账户 "${accountName}" 吗？此操作不可恢复。`)) {
            return;
        }

        try {
            const response = await fetch(`/api/config/accounts/${accountName}`, {
                method: "DELETE"
            });

            const data = await response.json();

            if (data.success) {
                alert(`✅ ${data.message}`);
                this.loadAccountsList();
            } else {
                alert(`❌ 删除失败: ${data.error}`);
            }
        } catch (error) {
            alert(`❌ 删除失败: ${error.message}`);
        }
    }
}

// 全局配置面板实例
let configPanel;

// 初始化应用
document.addEventListener("DOMContentLoaded", () => {
    new GeminiChat();
    configPanel = new ConfigPanel();
});
